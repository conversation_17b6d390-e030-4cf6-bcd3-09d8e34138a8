const fs = require('fs');
const path = require('path');

/**
 * Рекурсивно сканирует папку и находит все PNG файлы
 */
function findAllPngFiles(dir, baseDir = '') {
  const files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // Рекурсивно обходим подпапки
      const subFiles = findAllPngFiles(fullPath, baseDir);
      files.push(...subFiles);
    } else if (item.toLowerCase().endsWith('.png')) {
      // Создаем путь относительно public
      const relativePath = path.relative(baseDir, fullPath).replace(/\\/g, '/');
      files.push('/' + relativePath);
    }
  }

  return files;
}

// Основная логика
const texturesDir = path.join(__dirname, '..', 'public', 'textures');
const publicDir = path.join(__dirname, '..', 'public');
const outputFile = path.join(__dirname, '..', 'src', 'game', 'rendering', 'textures', 'texturesPaths.json');

console.log('Сканируем папку:', texturesDir);

if (!fs.existsSync(texturesDir)) {
  console.error('Папка textures не найдена:', texturesDir);
  process.exit(1);
}

// Сканируем все PNG файлы
const pngFiles = findAllPngFiles(texturesDir, publicDir);

console.log(`Найдено ${pngFiles.length} PNG файлов`);

// Создаем JSON объект
const textureData = {
  generatedAt: new Date().toISOString(),
  totalFiles: pngFiles.length,
  paths: pngFiles.sort() // Сортируем для удобства
};

// Создаем папку если не существует
const outputDir = path.dirname(outputFile);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Сохраняем в JSON файл
fs.writeFileSync(outputFile, JSON.stringify(textureData, null, 2), 'utf8');

console.log('JSON файл создан:', outputFile);
console.log('Первые 10 файлов:');
pngFiles.slice(0, 10).forEach(file => console.log('  ', file));

if (pngFiles.length > 10) {
  console.log(`  ... и еще ${pngFiles.length - 10} файлов`);
}
