import { Skeleton, WorldTransform } from "../../rendering/animations/animationSystem";
import { textureLoader } from "../textures/TextureLoader";

const boneTextureMap: Record<string, number> = {
  head: 1,
  rightThigh: 9,
  rightShin: 10,
  rightFoot: 14,
  leftThigh: 12,
  leftShin: 13,
  leftFoot: 11,
  torso: 2,
  leftShoulder: 3,
  leftForearm: 4,
  leftHand: 5,
  rightShoulder: 6,
  rightForearm: 7,
  rightHand: 8,
};

// Задаем явный порядок отрисовки костей для корректного отображения
const boneRenderOrder: string[] = [
  'leftThigh',
  'leftShin',
  'leftFoot',
  'rightThigh',
  'rightShin',
  'rightFoot',
  'torso',
  'leftShoulder',
  'leftForearm',
  'leftHand',
  'rightShoulder',
  'rightForearm',
  'rightHand',
  'head',
];

interface DrawCharacterClothesOptions {
  ctx: CanvasRenderingContext2D;
  skeleton: Skeleton;
  transforms: Record<string, WorldTransform>;
  equippedArmor?: string; // например: "underwear"
  view: 'front' | 'back'; // вид сзади или спереди
  zoom: number;
  drawOrder?: string[]; // Добавляем необязательный параметр
  animationName?: string; // Добавляем название анимации для определения направления
  animationFrame?: number; // Добавляем номер кадра анимации
  leftFootOffsetX?: number; // Смещение левой стопы по X
  rightFootOffsetX?: number; // Смещение правой стопы по X
}

// Используем централизованный textureLoader вместо локального кэша

export function drawEquippedTextures({
  ctx,
  skeleton,
  transforms,
  equippedArmor,
  view,
  zoom,
  drawOrder,
  animationName,
}: DrawCharacterClothesOptions) {
  if (!equippedArmor) return;

  // Исправляем путь к текстурам
  const basePath = `/textures/worldMap/armor/${equippedArmor}/${view}`;
  

  // Используем drawOrder из анимации, если он есть, иначе boneRenderOrder
  const currentDrawOrder = drawOrder || boneRenderOrder;

  currentDrawOrder.forEach((boneName) => {
    let textureIndex = boneTextureMap[boneName];
    
    // Изменяем текстуры стоп в зависимости от направления анимации
    if (animationName && (boneName === 'leftFoot' || boneName === 'rightFoot')) {
      if (animationName.includes('east')) {
        textureIndex = 14;
      } else if (animationName.includes('west')) {
        textureIndex = 11;
      }
    }
    
    const bone = skeleton[boneName];
    const transform = transforms[boneName];

    if (!bone || !transform || textureIndex === undefined) {
      return;
    }

    const texturePath = `${basePath}/${textureIndex}.png`;
    
    // Используем централизованный кэш
    const img = textureLoader.getTexture(texturePath);
    if (!img) {
      // Fire-and-forget загрузка через центральный loader
      textureLoader.loadTexture(texturePath).catch(() => {});
      return; // Текстура еще загружается или не найдена
    }

    ctx.save();
    ctx.translate(transform.x, transform.y);
    ctx.rotate(transform.rotation);
    
    // Натягиваем текстуру точно по размеру кости
    // Рисуем изображение с точными размерами прямоугольника кости
    ctx.drawImage(
      img, 
      -bone.width / 2,  // x - левый край
      -5,               // y - верхний край (как в drawBone)
      bone.width,       // ширина - точно по ширине кости
      bone.length       // высота - точно по длине кости
    );
    ctx.restore();
  });
}

export { boneTextureMap, boneRenderOrder };
export type { DrawCharacterClothesOptions };