import { PresetLocationMap } from "../presetType";

export const parkingPresets: PresetLocationMap[] = [
  {
    name: 'parking_1',
    width: 16,
  height: 10,
  tokenMap: [
    [10, 10, 10, 10, 10, 10, 10, 28, 10, 10, 10, 10, 10, 10, 10, 5, 10],
    [10, 35, 35, 28, 35, 35, 28, 28, 35, 35, 28, 29, 5, 35, 35, 28, 10],
    [10, 35, 35, 28, 35, 35, 28, 5, 35, 35, 28, 29, 28, 35, 35, 28, 10],
    [10, 35, 35, 28, 35, 35, 28, 28, 35, 35, 5, 28, 28, 35, 35, 28, 28],
    [5, 35, 35, 28, 35, 35, 5, 28, 35, 35, 28, 28, 999, 35, 35, 28, 10],
    [28, 5, 5, 28, 28, 28, 28, 28, 28, 28, 28, 28, 5, 5, 28, 5, 5],
    [10, 33, 28, 5, 28, 5, 28, 28, 28, 28, 5, 5, 28, 28, 28, 28, 10],
    [5, 33, 28, 28, 28, 28, 5, 28, 28, 28, 28, 28, 28, 28, 5, 999, 10],
    [10, 999, 28, 28, 999, 28, 28, 28, 5, 5, 28, 28, 28, 5, 28, 999, 10],
    [10, 35, 35, 35, 35, 28, 28, 35, 35, 35, 35, 28, 28, 5, 28, 999, 10],
    [10, 35, 35, 35, 35, 28, 5, 35, 35, 35, 35, 5, 999, 5, 28, 28, 10],
    [10, 10, 28, 28, 10, 28, 10, 10, 10, 10, 10, 10, 28, 999, 5, 5, 28]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];