# Set your host machine LAN IP (the IP other devices will use to reach this host)
# Example: HOST_LAN_IP=localhost
HOST_LAN_IP=localhost

# Frontend origin used for backend CORS checks. Usually http://<HOST_LAN_IP>:3000
# Example: FRONTEND_ORIGIN=http://localhost:3000
# FRONTEND_ORIGIN should point to the host's LAN address so other devices can access
FRONTEND_ORIGIN=http://localhost:3000

# NuclearStory Development Environment
NODE_ENV=development
POSTGRES_PASSWORD=password
POSTGRES_USER=nuclearstory
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
CORS_ORIGIN=${FRONTEND_ORIGIN}
LOG_LEVEL=INFO

# Database Configuration
POSTGRES_AUTH_DB=auth_db
POSTGRES_SAVES_DB=saves_db

# AI Configuration (using g4f - no API key needed)
# OpenAI API key is optional, g4f provides free alternatives
OPENAI_API_KEY=optional-openai-api-key

# Application Ports
AUTH_SERVICE_PORT=3001
GAME_ENGINE_SERVICE_PORT=3002
STORY_SERVICE_PORT=3003
SAVE_SERVICE_PORT=3004
AI_SERVICE_PORT=3005
FRONTEND_PORT=3000
