import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { WorldMapDecorations, TerrainType } from 'src/shared';
import { STABLE_DECORATION_CONFIG } from '../worldGeneratorConstants';
import {
  SimpleProgressTracker,
  CancellationToken
} from '../../utils/asyncUtils';

/**
 * Стабильная генерация декораций на основе размера карты
 * Гарантирует предсказуемое количество декораций и их равномерное распределение
 */
export async function generateStableDecorationsAsync(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  const totalCells = worldSize * worldSize;
  
  // Рассчитываем целевое количество клеток для каждого типа декорации
  const targetCounts = {
    FOREST: Math.floor(totalCells * STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS.FOREST),
    BUSHES: Math.floor(totalCells * STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS.BUSHES),
    SWAMP: Math.floor(totalCells * STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS.SWAMP),
    RUINS: Math.floor(totalCells * STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS.RUINS),
    RUBBLE: Math.floor(totalCells * STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS.RUBBLE),
  };


  // Генерируем каждый тип декорации
  const decorationTypes = [
    { name: 'Лес', type: 'FOREST' as const, decoration: WorldMapDecorations.FOREST },
    { name: 'Кусты', type: 'BUSHES' as const, decoration: WorldMapDecorations.BUSHES },
    { name: 'Болота', type: 'SWAMP' as const, decoration: WorldMapDecorations.SWAMP },
    { name: 'Руины', type: 'RUINS' as const, decoration: WorldMapDecorations.RUINS },
    { name: 'Мусор', type: 'RUBBLE' as const, decoration: WorldMapDecorations.RUBBLE },
  ];

  for (let i = 0; i < decorationTypes.length; i++) {
    const decorationType = decorationTypes[i];
    
   

    const progress = (i / decorationTypes.length) * 100;

    await generateDecorationTypeStable(
      grid,
      worldSize,
      decorationType.type,
      decorationType.decoration,
      targetCounts[decorationType.type],
      rng,
      cancellationToken
    );
  }

}

/**
 * Генерирует конкретный тип декорации с заданным целевым количеством
 */
async function generateDecorationTypeStable(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  decorationType: keyof typeof STABLE_DECORATION_CONFIG.DENSITY_COEFFICIENTS,
  decoration: WorldMapDecorations,
  targetCount: number,
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  const minDistance = STABLE_DECORATION_CONFIG.MIN_DISTANCE_BETWEEN_CENTERS[decorationType];
  const areaSize = STABLE_DECORATION_CONFIG.AREA_SIZES[decorationType];
  
  let placedCells = 0;
  let attempts = 0;
  const maxAttempts = targetCount * 3; // Ограничиваем количество попыток
  const placedCenters: Position[] = [];

  while (placedCells < targetCount && attempts < maxAttempts) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    attempts++;

    // Выбираем случайную позицию для центра
    const center: Position = {
      x: Math.floor(rng() * worldSize),
      y: Math.floor(rng() * worldSize)
    };

    // Проверяем, что центр достаточно далеко от других центров того же типа
    const tooClose = placedCenters.some(placedCenter => {
      const distance = Math.sqrt(
        (center.x - placedCenter.x) ** 2 + (center.y - placedCenter.y) ** 2
      );
      return distance < minDistance;
    });

    if (tooClose) {
      continue;
    }

    // Проверяем, что центр не занят
    const centerKey = `${center.x},${center.y}`;
    if (!grid[centerKey] || grid[centerKey].decoration !== WorldMapDecorations.NONE) {
      continue;
    }

    // Определяем размер области для этого центра
    const radius = areaSize.min + Math.floor(rng() * (areaSize.max - areaSize.min + 1));
    
    // Размещаем декорацию в области вокруг центра
    const cellsPlaced = await placeDecorationArea(
      grid,
      worldSize,
      center,
      radius,
      decoration,
      rng,
      cancellationToken
    );

    if (cellsPlaced > 0) {
      placedCenters.push(center);
      placedCells += cellsPlaced;
    }

    // Освобождаем event loop каждые 10 попыток
    if (attempts % 10 === 0) {
      await new Promise(resolve => setImmediate(resolve));
    }
  }

}

/**
 * Размещает декорацию в области вокруг центра
 * Устанавливает соответствующий terrain для каждой декорации
 */
async function placeDecorationArea(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  center: Position,
  radius: number,
  decoration: WorldMapDecorations,
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<number> {
  
  let cellsPlaced = 0;
  let processedCells = 0;

  for (let x = center.x - radius; x <= center.x + radius; x++) {
    for (let y = center.y - radius; y <= center.y + radius; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }

      // Проверяем границы карты
      if (x < 0 || x >= worldSize || y < 0 || y >= worldSize) {
        continue;
      }

      const distance = Math.sqrt((x - center.x) ** 2 + (y - center.y) ** 2);
      
      // Прове��яем, что точка в пределах радиуса
      if (distance > radius) {
        continue;
      }

      const cellKey = `${x},${y}`;
      
      // Проверяем, что ячейка существует и пуста
      if (!grid[cellKey] || grid[cellKey].decoration !== WorldMapDecorations.NONE) {
        continue;
      }

      // Рассчитываем шанс размещения в зависимости от расстояния от центра
      const distanceRatio = distance / radius;
      const baseChance = 90; // Базовый шанс размещения
      const chance = baseChance * (1 - distanceRatio * 0.5); // Уменьшаем шанс к краям

      if (rng() * 100 < chance) {
        grid[cellKey].decoration = decoration;
        
        // Устанавливаем соответствующий terrain для каждой декорации
        grid[cellKey].terrain = getTerrainForDecoration(decoration);
        
        cellsPlaced++;
      }

      processedCells++;
      
      // Освобождаем event loop каждые 100 ячеек
      if (processedCells % 100 === 0) {
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }

  return cellsPlaced;
}

/**
 * Возвращает соответствующий terrain для декорации
 */
function getTerrainForDecoration(decoration: WorldMapDecorations): TerrainType {
  switch (decoration) {
    case WorldMapDecorations.LAKE:
      return TerrainType.WATER;
    default:
      return TerrainType.WASTELAND; // Все остальные декорации на пустошах
  }
}

