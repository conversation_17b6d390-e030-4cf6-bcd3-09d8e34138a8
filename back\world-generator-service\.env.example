# Application
PORT=3003
NODE_ENV=development

# CORS (handled by docker-compose)
# CORS_ORIGIN=http://localhost:3000

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
AI_SERVICE_URL=http://localhost:3005

# AI Configuration (using g4f through AI service)
# No API keys needed - g4f provides free access
MAX_STORY_LENGTH=1000
DEFAULT_TEMPERATURE=0.7

# Redis (for caching stories)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Story Configuration
MAX_STORY_LENGTH=2000
STORY_CACHE_TTL=3600
ENABLE_STORY_MODERATION=true

# Logging
LOG_LEVEL=debug
