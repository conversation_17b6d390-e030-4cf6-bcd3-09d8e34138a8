import { TransferLocation, Point, PointWithId } from '../../shared/types/Location';
import { LocationDecorations, TerrainType, MaterialTexture, DecorationZoneType, LocationType } from '../../shared/enums';

// Небольшой, легко расширяемый набор правил для сопоставления декораций с типом поверхности
type DecoRule = {
  decorations: string[]; // ключи декораций для точного совпадения
  matchContains?: string[]; // подстроки названий декораций для поиска
  radius?: number; // радиус применения (чебышёв)
  terrain: TerrainType;
  forRoom?: boolean; // если true, закрасить всю комнату, содержащую декорацию
};

const DEFAULT_RULES: DecoRule[] = [
  { decorations: [LocationDecorations.GASSTATIONPUMP], radius: 0, terrain: TerrainType.ASPHALT },
  { decorations: [LocationDecorations.TOILET], terrain: TerrainType.TILES, forRoom: true },
  // обработка общих названий, содержащих 'asphalt' или 'road'
  { decorations: [], matchContains: ['asphalt', 'road'], radius: 0, terrain: TerrainType.ASPHALT }
];

// Сопоставление текстуры материала с типом поверхности по умолчанию для интерьера
export function materialToTerrain(m?: MaterialTexture): TerrainType {
  switch (m) {
    case MaterialTexture.BRICK:
      return TerrainType.WOOD;
    case MaterialTexture.BETON:
      return TerrainType.BETON;
    case MaterialTexture.WOOD:
      return TerrainType.WOOD;
    case MaterialTexture.METAL:
      return TerrainType.BETON;
    
  }
}

// Вспомогательная функция для генерации ключа по координатам
function key(x: number, y: number) { return `${x},${y}`; }


export function setLocationFloor(location: TransferLocation, rules: DecoRule[] = DEFAULT_RULES): void {
  if (!location) return;

  const size = location.locationSize;
  if (!size || size.length < 2) return;
  const width = size[0];
  const height = size[1];

  // Логика из прежней paintBuildingsBase: определение интерьеров зданий и
  // закраска базовой поверхности/стен/колец. Выполняется в начале для каждой локации,
  // особенно важна для обработки LocationType.OUTDOOR.
  // Гарантировать наличие контейнеров
  if (!location.decorations) location.decorations = {};
  if (!location.floor) {
    location.floor = {} as Record<TerrainType, Point[]>;
    for (const t of Object.values(TerrainType) as TerrainType[]) {
      location.floor[t] = [];
    }
  }

  // Стены/окна/двери считаются препятствиями для определения зданий
  const wallDecorationKeys = [
    LocationDecorations.WALL,
    LocationDecorations.WINDOW,
    LocationDecorations.DOOR,
    LocationDecorations.GARAGEDOOR,
    LocationDecorations.JAILDOOR,
    LocationDecorations.JAILWINDOW,
    LocationDecorations.JAILBARS
  ];
  const walls: Point[] = [];
  for (const wk of wallDecorationKeys) {
    const arr = (location.decorations[wk] || []) as Point[];
    if (arr && arr.length) walls.push(...arr);
  }
  const wallSet = new Set<string>(walls.map(([ax, ay]) => key(ax, ay)));

  // Заливка по периметру для пометки внешней области
  const outside = new Set<string>();
  const queue: Point[] = [];
  for (let xx= -1; xx < width; xx++) {
    for (const yy of [0, height - 1]) {
      if (!wallSet.has(key(xx, yy))) { queue.push([xx, yy]); outside.add(key(xx, yy)); }
    }
  }
  for (let yy= -1; yy < height; yy++) {
    for (const xx of [0, width - 1]) {
      if (!wallSet.has(key(xx, yy))) { queue.push([xx, yy]); outside.add(key(xx, yy)); }
    }
  }

  while (queue.length) {
    const [cx, cy] = queue.shift()!;
    const nbs = [[cx+1,cy],[cx-1,cy],[cx,cy+1],[cx,cy-1]];
    for (const [nx, ny] of nbs) {
      if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
      const k = key(nx, ny);
      if (outside.has(k) || wallSet.has(k)) continue;
      outside.add(k);
      queue.push([nx, ny]);
    }
  }

  // Внутренние тайлы — это не стены и не внешняя область
  const interiorSet = new Set<string>();
  for (let xx= -1; xx < width; xx++) for (let yy= -1; yy < height; yy++) {
    const k = key(xx, yy);
    if (wallSet.has(k)) continue;
    if (!outside.has(k)) interiorSet.add(k);
  }

  // Группировка внутренних областей в компоненты (здания)
  const visited = new Set<string>();
  const buildings: Point[][] = [];
  for (const s of interiorSet) {
    if (visited.has(s)) continue;
    const [sx, sy] = s.split(',').map(n => Number(n)) as [number, number];
    const bqueue: Point[] = [[sx, sy]];
    const comp: Point[] = [];
    visited.add(s);
    while (bqueue.length) {
      const [cx, cy] = bqueue.shift()!;
      comp.push([cx, cy]);
      const nbs = [[cx+1,cy],[cx-1,cy],[cx,cy+1],[cx,cy-1]];
      for (const [nx, ny] of nbs) {
        if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
        const nk = key(nx, ny);
        if (!interiorSet.has(nk) || visited.has(nk)) continue;
        visited.add(nk);
        bqueue.push([nx, ny]);
      }
    }
    buildings.push(comp);
  }
  // отладочные логи удалены

  // Вспомогательная функция для добавления точек в карту location.floor
  function addFloor(t: TerrainType, pts: Point[]) {
  // allow t === 0 (enum may be zero); only skip when pts missing/empty or t is null/undefined
  if (t === undefined || t === null || !pts || !pts.length) return;
    // гарантировать наличие контейнера
    if (!location.floor![t]) location.floor![t] = [];
    // удалить эти точки из других поверхностей, чтобы последняя закраска победила
    const keys = new Set(pts.map(p => key(p[0], p[1])));
    for (const ot of Object.values(TerrainType) as TerrainType[]) {
      if (ot === t) continue;
      const arr = location.floor![ot];
      if (!arr || !arr.length) continue;
      location.floor![ot] = arr.filter(p => !keys.has(key(p[0], p[1])));
    }
    location.floor![t].push(...pts);
  }

  // Закрасить каждый интерьер здания, прилегающие стены и внешнюю окантовку (1 тайл)
  const baseTerrain = materialToTerrain(location.textureMaterial);

  function paintBuildingOutline(building: Point[]) {
    if (!building || !building.length) return;

    // 1) покрасить интерьер здания
    addFloor(baseTerrain, building);

    // 2) найти уникальные стеновые тайлы, которые граничат с интерьером (4-ориентированные)
    const adjacentWalls = new Map<string, Point>();
    for (const [ix, iy] of building) {
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            if (dx === 0 && dy === 0) continue;
            const nx = ix + dx; const ny = iy + dy;
            if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
            const k = key(nx, ny);
            if (wallSet.has(k)) adjacentWalls.set(k, [nx, ny]);
          }
        }
    }
    const wallList = Array.from(adjacentWalls.values());
    if (wallList.length) addFloor(baseTerrain, wallList);

    // 3) собрать внешнюю окантовку — клетки вокруг этих стен, которые не являются стенами и не внутри
    const ring = new Map<string, Point>();
    for (const [wx, wy] of wallList) {
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          const nx = wx + dx; const ny = wy + dy;
          if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
          const nk = key(nx, ny);
          if (wallSet.has(nk)) continue; // не захватывать стены
          if (interiorSet.has(nk)) continue; // не захватывать внутренние клетки
          ring.set(nk, [nx, ny]);
        }
      }
    }
    if (ring.size) addFloor(TerrainType.TILES, Array.from(ring.values()));
  }

  for (const building of buildings) paintBuildingOutline(building);

  // Если зданий нет (interiorSet пуст) — закрашивать только для внутренних/подземных типов.
  // Для OUTDOOR-локаций не закрашивать всю карту как интерьер (чтобы не закрасить всю улицу бетоном).
  if (buildings.length === 0 && location.type !== undefined && location.type !== LocationType.OUTDOOR) {
    // Для закрытых внутренних комнат без явных стен — закрасить все не-стены базовым типом
    const fallback: Point[] = [];
    for (let x = 0; x < width; x++) for (let y = 0; y < height; y++) {
      const k = key(x, y);
      if (!wallSet.has(k)) fallback.push([x, y]);
    }
    addFloor(baseTerrain, fallback);
  }

  // Применить правила, основанные на декорациях (включая правила для комнат)
  // Построить карту декораций: ключ декорации -> точки
  const decorations = location.decorations || {};

  // Вспомогательная функция: найти клетки комнаты через decorationZoneType, если есть
  const roomZones = new Map<number, Point[]>();
  if (location.decorationZoneType) {
    for (const zoneKey of Object.keys(location.decorationZoneType)) {
      const entries = location.decorationZoneType[zoneKey as DecorationZoneType] || [] as PointWithId[];
      for (const [x, y, id] of entries) {
        if (!roomZones.has(id)) roomZones.set(id, []);
        roomZones.get(id)!.push([x, y]);
      }
    }
  }

  // отладочные логи удалены

  // Вспомогательная функция: определить клетки комнаты (включая координаты стен) для заданной точки декорации
  function getRoomPointsForDecoration(px: number, py: number): Point[] {
    // 1) Поиск через roomZones
    for (const [id, pts] of roomZones) {
      if (pts.some(p => p[0] === px && p[1] === py)) {
        const borderWalls: Point[] = [];
        for (const [rx, ry] of pts) {
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              if (dx === 0 && dy === 0) continue;
              const nx = rx + dx; const ny = ry + dy;
              const k = key(nx, ny);
              if (wallSet.has(k)) borderWalls.push([nx, ny]);
            }
          }
        }
        const uniqWalls = Array.from(new Map(borderWalls.map(p => [key(p[0], p[1]), p])).values());
        return pts.concat(uniqWalls);
      }
    }

    // 2) Компоненты зданий
    for (const building of buildings) {
      if (building.some(p => p[0] === px && p[1] === py)) {
        const borderWalls: Point[] = [];
        for (const [ix, iy] of building) {
          for (let dx= -1; dx <= 1; dx++) {
            for (let dy= -1; dy <= 1; dy++) {
              if (dx === 0 && dy === 0) continue;
              const nx = ix + dx; const ny = iy + dy;
              const k = key(nx, ny);
              if (wallSet.has(k)) borderWalls.push([nx, ny]);
            }
          }
        }
        const uniq = Array.from(new Map(borderWalls.map(p => [key(p[0], p[1]), p])).values());
        return building.concat(uniq);
      }
    }

    // 3) Попытка найти прямоугольную комнату, сканируя до ближайших стен в 4 направлениях
    let left= -1, right = -1, top= -1, bottom = -1;
    // сканировать влево
    for (let nx = px; nx >= 0; nx--) {
      if (wallSet.has(key(nx, py))) { left = nx; break; }
    }
    // сканировать вправо
    for (let nx = px; nx < width; nx++) {
      if (wallSet.has(key(nx, py))) { right = nx; break; }
    }
    // сканировать вверх
    for (let ny = py; ny >= 0; ny--) {
      if (wallSet.has(key(px, ny))) { top = ny; break; }
    }
    // сканировать вниз
    for (let ny = py; ny < height; ny++) {
      if (wallSet.has(key(px, ny))) { bottom = ny; break; }
    }

    if (left >= 0 && right >= 0 && top >= 0 && bottom >= 0) {
      // построить прямоугольник, включая стены
      const pts: Point[] = [];
      for (let rx = left; rx <= right; rx++) for (let ry = top; ry <= bottom; ry++) pts.push([rx, ry]);
      return pts;
    }

    // 4) запасной вариант: вернуть один тайл
    return [[px, py]];
  }

  // Применить правила
  for (const rule of rules) {
    // Проверить по точным ключам декораций
    const matchedPoints: Point[] = [];
    for (const decKey of Object.keys(decorations)) {
      const decList = decorations[decKey as any] || [] as Point[];
      const shouldMatchExact = rule.decorations && rule.decorations.length > 0 && rule.decorations.includes(decKey as any);
      const shouldMatchContains = rule.matchContains && rule.matchContains.some(s => decKey.toLowerCase().includes(s));
      if (!shouldMatchExact && !shouldMatchContains) continue;

      for (const [x, y] of decList as Point[]) matchedPoints.push([x, y]);
    }

    // применить точки
    for (const [x, y] of matchedPoints) {
      if (rule.forRoom) {
        const roomPts = getRoomPointsForDecoration(x, y);
        // краткий лог
        try {
          const walls = roomPts.filter(p => wallSet.has(key(p[0], p[1]))).length;
        } catch (e) { }
        addFloor(rule.terrain, roomPts);
        // создать окантовку толщиной 1 тайл вокруг комнаты и покрасить тем же материалом
        const ringMap = new Map<string, Point>();
        const roomSet = new Set(roomPts.map(p => key(p[0], p[1])));
        for (const [ix, iy] of roomPts) {
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              const nx = ix + dx; const ny = iy + dy;
              if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
              const nk = key(nx, ny);
              if (roomSet.has(nk)) continue; // не включать клетки комнаты
              if (interiorSet.has(nk) || wallSet.has(nk)) continue; // пропустить внутренние/стеновые тайлы
              ringMap.set(nk, [nx, ny]);
            }
          }
        }
        if (ringMap.size) addFloor(rule.terrain, Array.from(ringMap.values()));
      } else {
        const r = rule.radius || 0;
        const cells: Point[] = [];
        for (let dx = -r; dx <= r; dx++) {
          for (let dy = -r; dy <= r; dy++) {
            const nx = x + dx; const ny = y + dy;
            if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
            // Радиус по Чебышёву
            if (Math.max(Math.abs(dx), Math.abs(dy)) > r) continue;
            cells.push([nx, ny]);
          }
        }
        addFloor(rule.terrain, cells);
      }
    }
  }

  // Постобработка для сельскохозяйственных культур: покраска в GROUND в радиусе 1 клетки
  const cropDecorations = [
    LocationDecorations.WITHEREDCROP,
    LocationDecorations.TOMATO,
    LocationDecorations.PEPPER,
    LocationDecorations.CORN,
    LocationDecorations.POTATO,
    LocationDecorations.BEENS,
    LocationDecorations.ONION,
    LocationDecorations.CABBAGE,
    LocationDecorations.WHEAT,
    LocationDecorations.HOP
  ];

  const cropPoints: Point[] = [];
  for (const cropType of cropDecorations) {
    const crops = decorations[cropType] || [];
    for (const [x, y] of crops as Point[]) {
      cropPoints.push([x, y]);
    }
  }

  if (cropPoints.length > 0) {
    const groundPoints: Point[] = [];
    for (const [cx, cy] of cropPoints) {
      // Добавляем радиус 1 клетки вокруг культуры
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          const nx = cx + dx;
          const ny = cy + dy;
          if (nx >= 0 && ny >= 0 && nx < width && ny < height) {
            groundPoints.push([nx, ny]);
          }
        }
      }
    }
    addFloor(TerrainType.GROUND, groundPoints);
  }

  // Постобработка для городов и деревень: заполнение узких полосок WASTELAND между TILES
  if (location.subtype === 'town' || location.subtype === 'village') {
    // Отладочная информация
    const tilesCount = location.floor[TerrainType.TILES]?.length || 0;
    const wastelandCount = location.floor[TerrainType.WASTELAND]?.length || 0;
    
    fillNarrowWastelandGaps(location, width, height, addFloor);
    
    const tilesCountAfter = location.floor[TerrainType.TILES]?.length || 0;
    const wastelandCountAfter = location.floor[TerrainType.WASTELAND]?.length || 0;
  }

  // Удалить дубликаты точек в каждом списке поверхности
  for (const t of Object.values(TerrainType) as TerrainType[]) {
    const map = new Map<string, Point>();
    for (const [x, y] of location.floor![t] || []) map.set(key(x, y), [x, y]);
    location.floor![t] = Array.from(map.values());
  }
}

// Оптимизированная функция для заполнения узких пустот между областями TILES (шириной 1-5 тайлов)
function fillNarrowWastelandGaps(location: TransferLocation, width: number, height: number, addFloor: (t: TerrainType, pts: Point[]) => void): void {
  // Собираем TILES точки в быстрый массив для проверки
  const tilesPoints = location.floor?.[TerrainType.TILES] || [];
  const tilesGrid = new Array(height).fill(null).map(() => new Array(width).fill(false));
  for (const [x, y] of tilesPoints) {
    if (x >= 0 && x < width && y >= 0 && y < height) {
      tilesGrid[y][x] = true;
    }
  }

  // Собираем все покрашенные точки в 2D массив для быстрой проверки
  const paintedGrid = new Array(height).fill(null).map(() => new Array(width).fill(false));
  for (const terrainType of Object.values(TerrainType) as TerrainType[]) {
    const points = location.floor?.[terrainType] || [];
    for (const [x, y] of points) {
      if (x >= 0 && x < width && y >= 0 && y < height) {
        paintedGrid[y][x] = true;
      }
    }
  }

  const pointsToConvertToTiles: Point[] = [];
  const processedGrid = new Array(height).fill(null).map(() => new Array(width).fill(false));
  const carPlacementAreas: Set<string> = new Set(); // Отслеживаем клетки, где размещены машины

  // Один проход для поиска всех полосок
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      // Пропускаем покрашенные или уже обработанные клетки
      if (paintedGrid[y][x] || processedGrid[y][x]) continue;

      let foundStrip = false; // Флаг, чтобы избежать двойной обработки

      // Проверяем горизонтальную полоску
      let horizontalLength = 0;
      let tempX = x;
      while (tempX < width && !paintedGrid[y][tempX] && !processedGrid[y][tempX]) {
        horizontalLength++;
        tempX++;
      }
      
      // Если полоска подходящего размера и окружена TILES
      if (horizontalLength >= 1 && horizontalLength <= 5) {
        const hasLeftTiles = x > 0 && tilesGrid[y][x - 1];
        const hasRightTiles = tempX < width && tilesGrid[y][tempX];
        
        if (hasLeftTiles && hasRightTiles) {
          foundStrip = true;
          
          // Добавляем всю горизонтальную полоску
          for (let i = 0; i < horizontalLength; i++) {
            pointsToConvertToTiles.push([x + i, y]);
            processedGrid[y][x + i] = true;
          }
          
          // Если ширина > 4, определяем количество машин для размещения (горизонтальная ориентация: 2x5)
          if (horizontalLength > 4) {
            const carWidth = 2;
            const carHeight = 5;
            
            // Определяем количество машин: 40% - 1 машина, 30% - 2 машины, 30% - 0 машин
            let targetCars = 0;
            const rand = Math.random();
            if (rand < 0.4) {
              targetCars = 1;
            } else if (rand < 0.7) {
              targetCars = 2;
            } else {
              targetCars = 0;
            }
            
            let placedCars = 0;
            
            // Пытаемся разместить нужное количество машин
            for (let attempt = 0; attempt < targetCars && placedCars < targetCars; attempt++) {
              let centerX: number;
              
              if (targetCars === 1) {
                // Одна машина - размещаем по центру
                centerX = x + Math.floor((horizontalLength - carWidth) / 2);
              } else {
                // Две машины - размещаем с равными отступами
                if (attempt === 0) {
                  centerX = x + Math.floor(horizontalLength / 4) - Math.floor(carWidth / 2);
                } else {
                  centerX = x + Math.floor(3 * horizontalLength / 4) - Math.floor(carWidth / 2);
                }
              }
              
              // Проверяем, помещается ли машина в пределах полоски по вертикали
              const minCarY = Math.max(0, y - Math.floor(carHeight / 2));
              const maxCarY = Math.min(height - carHeight, y + Math.floor(carHeight / 2));
              const carY = Math.max(minCarY, Math.min(maxCarY, y - Math.floor(carHeight / 2)));
              
              // Дополнительная проверка: машина должна быть полностью в пределах карты
              if (centerX >= 0 && centerX + carWidth <= width && 
                  carY >= 0 && carY + carHeight <= height) {
                
                // Проверяем, можно ли разместить машину (включая проверку препятствий в радиусе)
                if (canPlaceCarInGapWithObstacleCheck(location, centerX, carY, carWidth, carHeight, width, height)) {
                  addCarToLocation(location, centerX, carY, carWidth, carHeight);
                  placedCars++;
                  
                  // Отмечаем клетки машины, чтобы не удалять их как декорации
                  for (let dx = 0; dx < carWidth; dx++) {
                    for (let dy = 0; dy < carHeight; dy++) {
                      carPlacementAreas.add(key(centerX + dx, carY + dy));
                    }
                  }
                }
              }
            }
          }
        }
      }

      // Проверяем вертикальную полоску только если горизонтальная не найдена
      if (!foundStrip) {
        let verticalLength = 0;
        let tempY = y;
        while (tempY < height && !paintedGrid[tempY][x] && !processedGrid[tempY][x]) {
          verticalLength++;
          tempY++;
        }
        
        // Если полоска подходящего размера и окружена TILES
        if (verticalLength >= 1 && verticalLength <= 5) {
          const hasTopTiles = y > 0 && tilesGrid[y - 1][x];
          const hasBottomTiles = tempY < height && tilesGrid[tempY][x];
          
          if (hasTopTiles && hasBottomTiles) {
            // Добавляем всю вертикальную полоску
            for (let i = 0; i < verticalLength; i++) {
              pointsToConvertToTiles.push([x, y + i]);
              processedGrid[y + i][x] = true;
            }
            
            // Если высота > 4, определяем количество машин для размещения (вертикальная ориентация: 5x2)
            if (verticalLength > 4) {
              const carWidth = 5;
              const carHeight = 2;
              
              // Определяем количество машин: 40% - 1 машина, 30% - 2 машины, 30% - 0 машин
              let targetCars = 0;
              const rand = Math.random();
              if (rand < 0.4) {
                targetCars = 1;
              } else if (rand < 0.7) {
                targetCars = 2;
              } else {
                targetCars = 0;
              }
              
              let placedCars = 0;
              
              // Пытаемся разместить нужное количество машин
              for (let attempt = 0; attempt < targetCars && placedCars < targetCars; attempt++) {
                let centerY: number;
                
                if (targetCars === 1) {
                  // Одна машина - размещаем по центру
                  centerY = y + Math.floor((verticalLength - carHeight) / 2);
                } else {
                  // Две машины - размещаем с равными отступами
                  if (attempt === 0) {
                    centerY = y + Math.floor(verticalLength / 4) - Math.floor(carHeight / 2);
                  } else {
                    centerY = y + Math.floor(3 * verticalLength / 4) - Math.floor(carHeight / 2);
                  }
                }
                
                // Проверяем, помещается ли машина в пределах полоски по горизонтали
                const minCarX = Math.max(0, x - Math.floor(carWidth / 2));
                const maxCarX = Math.min(width - carWidth, x + Math.floor(carWidth / 2));
                const carX = Math.max(minCarX, Math.min(maxCarX, x - Math.floor(carWidth / 2)));
                
                // Дополнительная проверка: машина должна быть полностью в пределах карты
                if (carX >= 0 && carX + carWidth <= width && 
                    centerY >= 0 && centerY + carHeight <= height) {
                  
                  // Проверяем, можно ли разместить машину (включая проверку препятствий в радиусе)
                  if (canPlaceCarInGapWithObstacleCheck(location, carX, centerY, carWidth, carHeight, width, height)) {
                    addCarToLocation(location, carX, centerY, carWidth, carHeight);
                    placedCars++;
                    
                    // Отмечаем клетки машины, чтобы не удалять их как декорации
                    for (let dx = 0; dx < carWidth; dx++) {
                      for (let dy = 0; dy < carHeight; dy++) {
                        carPlacementAreas.add(key(carX + dx, centerY + dy));
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // Конвертируем найденные точки - красим все в TILES, а потом исключаем машины только из удаления декораций
  if (pointsToConvertToTiles.length > 0) {
    // Красим в TILES ВСЕ точки полоски (включая клетки под машинами)
    addFloor(TerrainType.TILES, pointsToConvertToTiles);
    
    // Добавляем окантовку +1 клетка во все стороны от полосок
    const ringPoints: Point[] = [];
    const originalPointsSet = new Set(pointsToConvertToTiles.map(([x, y]) => key(x, y)));
    
    for (const [px, py] of pointsToConvertToTiles) {
      // Проверяем все 8 направлений вокруг каждой точки полоски
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          if (dx === 0 && dy === 0) continue; // Пропускаем центральную точку
          
          const nx = px + dx;
          const ny = py + dy;
          
          // Проверяем границы карты
          if (nx < 0 || ny < 0 || nx >= width || ny >= height) continue;
          
          const neighborKey = key(nx, ny);
          
          // Не добавляем точки, которые уже в полоске
          if (originalPointsSet.has(neighborKey)) continue;
          
          // Не добавляем точки, которые уже покрашены в любой тип поверхности
          if (paintedGrid[ny][nx]) continue;
          
          // Добавляем в окантовку
          ringPoints.push([nx, ny]);
        }
      }
    }
    
    // Удаляем дубликаты из окантовки
    const uniqueRingPoints = Array.from(new Map(ringPoints.map(p => [key(p[0], p[1]), p])).values());
    
    // Красим окантовку в TILES
    if (uniqueRingPoints.length > 0) {
      addFloor(TerrainType.TILES, uniqueRingPoints);
    }
    
    // Фильтруем точки для операций с декорациями, исключая клетки где размещены машины
    const filteredPointsForDecorations = pointsToConvertToTiles.filter(([x, y]) => !carPlacementAreas.has(key(x, y)));
    
    if (filteredPointsForDecorations.length > 0) {
      // Убираем декорации только с клеток БЕЗ машин
      removeDecorationsFromPoints(location, filteredPointsForDecorations);
      
      // Добавляем случайные декорации только на клетки БЕЗ машин
      addRandomDecorationsToPoints(location, filteredPointsForDecorations);
    }
  } 
}

// Проверяет, можно ли разместить машину с учетом препятствий в радиусе 1 клетки
function canPlaceCarInGapWithObstacleCheck(location: TransferLocation, x: number, y: number, width: number, height: number, mapWidth: number, mapHeight: number): boolean {
  // Проверяем границы карты
  if (x < 0 || y < 0 || x + width > mapWidth || y + height > mapHeight) {
    return false;
  }

  // Проверяем, нет ли уже машины в этой области (улучшенная проверка пересечений)
  const cars = location.decorations?.[LocationDecorations.CAR] || [];
  for (const [carX, carY] of cars as Point[]) {
    // Проверяем пересечение прямоугольников
    if (!(x + width <= carX || x >= carX + 1 || y + height <= carY || y >= carY + 1)) {
      return false; // Пересечение с существующей машиной
    }
  }

  // Проверяем радиус 1 клетки вокруг габаритов машины на наличие препятствий
  const obstacleDecorations = [
    LocationDecorations.WALL,
    LocationDecorations.BENCH, 
    LocationDecorations.DOOR,
    LocationDecorations.CAR,
    LocationDecorations.GARAGEDOOR,
    LocationDecorations.JAILDOOR,
    LocationDecorations.WINDOW,
    LocationDecorations.JAILWINDOW,
    LocationDecorations.JAILBARS
  ];

  // Расширенная область проверки (габариты машины + 1 клетка по всем сторонам)
  const checkX1 = Math.max(0, x - 1);
  const checkY1 = Math.max(0, y - 1);
  const checkX2 = Math.min(mapWidth - 1, x + width);
  const checkY2 = Math.min(mapHeight - 1, y + height);

  for (const obstacleDeco of obstacleDecorations) {
    const obstacles = location.decorations?.[obstacleDeco] || [];
    for (const [obsX, obsY] of obstacles as Point[]) {
      // Проверяем пересечение с расширенной областью
      if (obsX >= checkX1 && obsX <= checkX2 && obsY >= checkY1 && obsY <= checkY2) {
        return false; // Есть препятствие в радиусе 1 клетки
      }
    }
  }

  return true;
}

// Добавляет все клетки многоблочного автомобиля
function addCarToLocation(location: TransferLocation, x: number, y: number, width: number, height: number): void {
  if (!location.decorations) {
    location.decorations = {};
  }
  if (!location.decorations[LocationDecorations.CAR]) {
    location.decorations[LocationDecorations.CAR] = [];
  }
  
  for (let dx = 0; dx < width; dx++) {
    for (let dy = 0; dy < height; dy++) {
      (location.decorations[LocationDecorations.CAR] as Point[]).push([x + dx, y + dy]);
    }
  }
}

// Убираем все декорации с указанных точек
function removeDecorationsFromPoints(location: TransferLocation, points: Point[]): void {
  const pointsToRemove = new Set(points.map(([x, y]) => key(x, y)));
  
  if (!location.decorations) return;
  
  for (const decorationType of Object.keys(location.decorations)) {
    const decorationPoints = location.decorations[decorationType as any] || [];
    location.decorations[decorationType as any] = decorationPoints.filter(
      ([x, y]: Point) => !pointsToRemove.has(key(x, y))
    );
  }
}

// Добавляем случайные декорации на указанные точки
function addRandomDecorationsToPoints(location: TransferLocation, points: Point[]): void {
  const streetDecorations = [
    LocationDecorations.TIRE,
    LocationDecorations.BARREL,
    LocationDecorations.PUDDLE,
    LocationDecorations.LITTER,
    LocationDecorations.TRASHCONTAINER,
    LocationDecorations.UNIVERSALRND,
    LocationDecorations.BOX,
    LocationDecorations.RUIN,
    LocationDecorations.SCELETON,
    LocationDecorations.SAFE,
    LocationDecorations.PALLET
  ];
  
  if (!location.decorations) location.decorations = {};
  
  for (const [x, y] of points) {
    // 30% шанс поставить декорацию
    if (Math.random() < 0.3) {
      const randomDecoration = streetDecorations[Math.floor(Math.random() * streetDecorations.length)];
      
      if (!location.decorations[randomDecoration]) {
        location.decorations[randomDecoration] = [];
      }
      
      location.decorations[randomDecoration].push([x, y]);
    }
  }
}

export default setLocationFloor;
