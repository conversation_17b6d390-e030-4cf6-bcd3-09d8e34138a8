import * as React from 'react'
import { useRef, useState, useCallback, useEffect, useMemo } from 'react'
import styles from './IsometricWorldMap.module.css'
import { WorldMap } from '../../../../shared/types/World'
import { useGameStore } from '@/game/store/gameStore'
import { useSoundManager, SoundChannel } from '@/game/audio'
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  MIN_ZOOM,
  MAX_ZOOM,
  ZOOM_STEP,
  IMCOMP_MAX_WIDTH,
  IMCOMP_MAX_HEIGHT
} from '../../../utils/constants/rendering'
import { useContextMenuDisable, useTerrainTextures, useDecorationTextures } from '../../hooks'
import {
  useCameraCenter,
  useCameraUI,
  useKeyboardControls,
  useCameraToPlayer
} from '../../../systems/camera/hooks'
import {
  useRenderLoop,
  useInitialDraw,
  useAdaptiveScreenSize,
  useCellTarget
} from '../../hooks'
import { getCameraDisplayCoordinates } from '../../../utils/coordinates/isometric'
import {
  createMouseMoveHandler,
  createZoomChangeHandler,
  createClickHandler,
  createRightClickHandlers,
  CameraRef
} from '../../../systems/interaction/eventHandlers'

import { createDrawFunction } from '../drawEngine'
import { createLocationDrawFunction } from '../../IsometricLocations/drawEngineLocInterior'
import { InfoTerminal } from '../../../ui/panels/InfoTerminal'
import { ContextMenu } from '../../../ui/controls/ContextMenu'
import {
  getCurrentPath,
  clearPath,
  revealFogOfWar,
  convertSpeedToMs
} from '../../../systems/movement/playerMovement'
import {
  generateInspectMessage,
  generateMovementCostMessage
} from '../../../ui/panels/InfoTerminal/messages'
import { useGameTime } from '../../hooks'
import { InfoPanel } from '../../../ui/panels/InfoPanel'
import { usePlayerPathMovement } from '@/game/systems/movement/hooks'
import { createLocationClickHandler, createLocationRightClickHandlers, createLocationKeyDownHandler } from '@/game/systems/location/eventHandlersLocation'
import { TimeControls } from '../../../ui/time/TimeControls'
import { SystemMenu } from '../../../ui/system/SystemMenu'
import { Camera, Search } from 'lucide-react'
import { isoToScreen, getTileCenterOnScreen } from '@/game/utils/coordinates/isometric';
import { BASE_TILE_HEIGHT_LOCATION, BASE_TILE_WIDTH_LOCATION } from '@/game/utils/constants/renderingLocation'
import { DialogBubblesLayer } from '@/game/ui/dialogs/DialogBubble'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
  onSave?: () => void
  onLoad?: () => void
  onSettings?: () => void
  onExit?: () => void
  onToggleMute?: () => void
  isMuted?: boolean
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  currentWorld,
  onSave = () => {},
  onLoad = () => {},
  onSettings = () => {},
  onExit = () => {},
  onToggleMute = () => {},
  isMuted = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const cameraRef = useRef<CameraRef>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState({ x: 0, y: 0 })
  const { width, height } = useAdaptiveScreenSize(IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT)
  const [cellTarget, setCellTarget] = useCellTarget()
  const { currentWorld: storeCurrentWorld, setCurrentWorld, playerLocationPresent, setPlayerLocationPresent } = useGameStore()
  const gameTime = useGameTime()
  const soundManager = useSoundManager()
  
  // Создаем стабильную ссылку на soundManager
  const soundManagerRef = useRef(soundManager)
  soundManagerRef.current = soundManager

  // Состояние для управления ambient музыкой
  const [currentAmbientTrack, setCurrentAmbientTrack] = useState(() => {
    // Начинаем с рандомного трека (1, 2 или 3)
    return Math.floor(Math.random() * 3) + 1
  })
  const ambientTracks = ['/sound/ambient/1.ogg', '/sound/ambient/2.ogg', '/sound/ambient/3.ogg']

  // Функция для воспроизведения следующего ambient трека
  const playNextAmbientTrack = useCallback(() => {
    const trackPath = ambientTracks[currentAmbientTrack - 1]
    
    soundManagerRef.current.playSound(
      trackPath,
      SoundChannel.AMBIENT,
      {
        onEnd: () => {
          // Переходим к следующему треку (циклически)
          const nextTrack = currentAmbientTrack >= 3 ? 1 : currentAmbientTrack + 1
          setCurrentAmbientTrack(nextTrack)
        }
      },
      `ambientGame${currentAmbientTrack}`
    )
  }, [currentAmbientTrack]) // Теперь зависит только от currentAmbientTrack

  // Запуск ambient музыки при монтировании компонента
  useEffect(() => {
    // Останавливаем музыку главного меню если она играет
    soundManagerRef.current.stopTrack('mainMenu')
    
    // Очистка при размонтировании
    return () => {
      soundManagerRef.current.stopChannel(SoundChannel.AMBIENT)
    }
  }, []) // Пустой массив зависимостей

  // Эффект для автоматического воспроизведения следующего трека
  useEffect(() => {
    if (currentAmbientTrack > 0) { // Убираем проверку > 1, так как теперь стартуем с рандомного
      const trackPath = ambientTracks[currentAmbientTrack - 1]
      
      soundManagerRef.current.playSound(
        trackPath,
        SoundChannel.AMBIENT,
        {
          onEnd: () => {
            // Переходим к следующему треку (циклически)
            const nextTrack = currentAmbientTrack >= 3 ? 1 : currentAmbientTrack + 1
            setCurrentAmbientTrack(nextTrack)
          }
        },
        `ambientGame${currentAmbientTrack}`
      )
    }
  }, [currentAmbientTrack]) // Зависит только от currentAmbientTrack

  // Состояние для контекстного меню
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    tileData: { isoX: number; isoY: number; tileData: any } | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    tileData: null
  })

  // Функция для добавления сообщений в терминал
  const [addTerminalMessage, setAddTerminalMessage] = useState<((message: string) => void) | null>(null)

  // Простой зум через canvas scale - размеры тайлов остаются базовыми
  // Если персонаж в локации — используем размеры локации
  const { tileWidth, tileHeight } = playerLocationPresent
    ? { tileWidth: BASE_TILE_WIDTH_LOCATION, tileHeight: BASE_TILE_HEIGHT_LOCATION }
    : { tileWidth: BASE_TILE_WIDTH, tileHeight: BASE_TILE_HEIGHT }

  // Предзагружаем текстуры местности и декораций
  useTerrainTextures()
  useDecorationTextures()

  // Туман войны теперь реализован через простую прозрачность - как в Fallout!

  // Персистентные функции отрисовки (чтобы не пересоздавать factory каждый кадр)
  const worldDrawFnRef = useRef<(() => void) | null>(null);
  const locationDrawFnRef = useRef<(() => void) | null>(null);

  // Обновляем world draw при изменении зависимостей
  useEffect(() => {
    worldDrawFnRef.current = createDrawFunction(
      canvasRef,
      cameraRef,
      currentWorld,
      tileWidth,
      tileHeight,
      width,
      height,
      cellTarget,
      zoom,
      gameTime
    );
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, cellTarget, zoom, gameTime]);

  // Обновляем location draw при изменении зависимостей или входе/выходе из локации
  useEffect(() => {
    if (playerLocationPresent) {
      const currentLocation = useGameStore.getState().currentLocation;
      locationDrawFnRef.current = createLocationDrawFunction(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        cellTarget,
        zoom,
        useGameStore.getState().currentLocation,
        gameTime
      );
    } else {
      locationDrawFnRef.current = null;
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, cellTarget, zoom, playerLocationPresent, gameTime]);

  const draw = useCallback(() => {
    worldDrawFnRef.current && worldDrawFnRef.current();
  }, []);

  const drawLocation = useCallback(() => {
    locationDrawFnRef.current && locationDrawFnRef.current();
  }, []);

  // Создаем объединенную функцию рендера
  const renderFrame = useCallback(() => {
    // Всегда вызываем world map рендер (он сам проверяет playerLocationPresent и может не рендерить)
    draw();
    // И также вызываем location рендер (он сам проверяет playerLocationPresent)
    drawLocation();
  }, [draw, drawLocation])

  // Хук для выбранной клетки

  // Создаем обработчики событий
  const handleZoomChange = createZoomChangeHandler(setZoom)
  const handleMouseMove = useCallback(() => {
    return createMouseMoveHandler(
      cameraRef,
      canvasRef
    );
  }, [cameraRef, canvasRef])
  
  const handleClick = useCallback(() => {
    // Используем разные обработчики кликов в зависимости от того, в локации ли игрок
    if (playerLocationPresent) {
      return createLocationClickHandler(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        setCellTarget,
        cellTarget,
        zoom
      );
    } else {
      return createClickHandler(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        setCellTarget,
        cellTarget,
        zoom
      );
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, setCellTarget, cellTarget, zoom, playerLocationPresent])

  // Обработчики правого клика с отслеживанием времени зажатия
  const rightClickHandlers = useMemo(() => {
    // Используем разные обработчики правого клика в зависимости от того, в локации ли игрок
    if (playerLocationPresent) {
      return createLocationRightClickHandlers(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        zoom,
        (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => {
          setContextMenu({
            visible: true,
            x,
            y,
            tileData
          })
        }
      );
    } else {
      return createRightClickHandlers(
        canvasRef,
        cameraRef,
        currentWorld,
        tileWidth,
        tileHeight,
        width,
        height,
        zoom,
        (x: number, y: number, tileData: { isoX: number; isoY: number; tileData: any }) => {
          setContextMenu({
            visible: true,
            x,
            y,
            tileData
          })
        }
      );
    }
  }, [canvasRef, cameraRef, currentWorld, tileWidth, tileHeight, width, height, zoom, playerLocationPresent])

  // Обработчики контекстного меню
  const handleContextMenuClose = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }))
  }, [])

  const handleInspect = useCallback(() => {
    if (contextMenu.tileData && addTerminalMessage) {
      const message = generateInspectMessage(contextMenu.tileData)
      addTerminalMessage(message)
    }
    handleContextMenuClose()
  }, [contextMenu.tileData, addTerminalMessage, handleContextMenuClose])

  const handleMovementCost = useCallback(() => {
    if (contextMenu.tileData && addTerminalMessage) {
      const message = generateMovementCostMessage(contextMenu.tileData)
      addTerminalMessage(message)
    }
    handleContextMenuClose()
  }, [contextMenu.tileData, addTerminalMessage, handleContextMenuClose])

  // Колбэки для движения игрока
  // Хук для движения игрока по пути
  const currentPath = getCurrentPath()
  const playerPosition = currentWorld?.player?.position || { x: 0, y: 0 }
  // Получаем скорость из настроек мира и конвертируем в миллисекунды
  const movementSpeed = convertSpeedToMs(currentWorld?.settings?.worldMapPlayerSpeed || 3)
  
  usePlayerPathMovement(playerPosition, currentPath, movementSpeed, currentWorld)

  // Видео тумана войны теперь рендерится в канвасе

  // Подключаем хуки
  useCameraCenter(currentWorld, cameraRef, tileWidth, tileHeight)
  useRenderLoop(renderFrame)
  useCameraUI(cameraRef, setCameraUI)
  useKeyboardControls(cameraRef)
  
  // Хук для перемещения камеры на игрока
  const centerCameraOnPlayer = useCameraToPlayer(currentWorld, cameraRef, tileWidth, tileHeight)
  
  // Слушатель для автоматического перемещения камеры на игрока при входе/выходе из локации
  useEffect(() => {
    const handleCenterCamera = () => {
      centerCameraOnPlayer();
    };
    
    window.addEventListener('centerCameraOnPlayer', handleCenterCamera);
    
    return () => {
      window.removeEventListener('centerCameraOnPlayer', handleCenterCamera);
    };
  }, [centerCameraOnPlayer])
  
  // Обработчик клавиш для локаций (вход и выход)
  useEffect(() => {
    const handleLocationKeyDown = createLocationKeyDownHandler(currentWorld);
    window.addEventListener('keydown', handleLocationKeyDown);
    return () => {
      window.removeEventListener('keydown', handleLocationKeyDown);
    };
  }, [currentWorld])
  
  // Простой обработчик колеса мыши для зума
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      const zoomDelta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
      const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoom + zoomDelta));
      setZoom(newZoom);
    };

    canvas.addEventListener('wheel', handleWheel, { passive: false });
    return () => canvas.removeEventListener('wheel', handleWheel);
  }, [zoom])
  useContextMenuDisable(canvasRef)
  useInitialDraw(renderFrame)

  // Обновляем позицию контекстного меню при движении камеры или изменении зума
  useEffect(() => {
    if (!contextMenu.visible || !contextMenu.tileData) return;
    
    const { isoX, isoY } = contextMenu.tileData;
    if (isoX == null || isoY == null) return;
    
    const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
    const { centerX, centerY } = getTileCenterOnScreen(
      screenX,
      screenY,
      width,
      height,
      cameraUI.x,
      cameraUI.y
    );
    
    // Обновляем позицию только если она изменилась
    if (contextMenu.x !== centerX || contextMenu.y !== centerY) {
      setContextMenu((prev) => ({ 
        ...prev, 
        x: centerX, 
        y: centerY 
      }));
    }
  }, [contextMenu.visible, contextMenu.tileData, contextMenu.x, contextMenu.y, tileWidth, tileHeight, width, height, cameraUI.x, cameraUI.y, zoom])

  return (
    <div className={styles.mapContainer}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={`${styles.mapCanvas} ${styles.canvasDefault}`}
        onMouseMove={handleMouseMove()}
        onClick={handleClick()}
        onMouseDown={rightClickHandlers.handleMouseDown}
        onMouseUp={rightClickHandlers.handleMouseUp}
        onContextMenu={rightClickHandlers.handleContextMenu}
      />
      <InfoPanel 
        displayCoordinates={getCameraDisplayCoordinates(
          cameraUI.x,
          cameraUI.y,
          currentWorld?.settings?.worldSize || 20,
          tileWidth,
          tileHeight
        )}
        zoom={zoom}
        onZoomChange={handleZoomChange}
        onCenterOnPlayer={centerCameraOnPlayer}
      />
      <InfoTerminal 
        cellTarget={cellTarget} 
        onAddMessage={(fn) => setAddTerminalMessage(() => fn)}
      />
      <ContextMenu
        x={contextMenu.x}
        y={contextMenu.y}
        visible={contextMenu.visible}
        onClose={handleContextMenuClose}
        onInspect={handleInspect}
        onMovementCost={handleMovementCost}
      />
      <DialogBubblesLayer 
        width={width} 
        height={height} 
        tileWidth={tileWidth} 
        tileHeight={tileHeight} 
        cameraX={cameraUI.x} 
        cameraY={cameraUI.y} 
        zoom={zoom} 
        playerLocationPresent={playerLocationPresent}
      />
      
      {/* Встроенные HUD компоненты */}
      <div className={styles.hudTopLeft}>
        <TimeControls />
      </div>
      <div className={styles.cameraButton} onClick={() => centerCameraOnPlayer()}>
        <Search size={28} />
      </div>
  <div className={styles.hudBottomRight}>
        <SystemMenu
          onSave={onSave}
          onLoad={onLoad}
          onSettings={onSettings}
          onExit={onExit}
          onToggleMute={() => soundManager.setGlobalMute(!soundManager.globalMute)}
          isMuted={soundManager.globalMute}
        />
      </div>
    </div>
  )
}

export default IsometricMap