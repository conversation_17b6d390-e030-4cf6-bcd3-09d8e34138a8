import { PresetLocationMap } from "../presetType";

export const fieldPresets: PresetLocationMap[] = [
  {
    name: 'fieldCorn_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 83, 83, 83, 83, 83, 999, 999, 83, 83, 83, 83, 83, 83, 83, 83, 83, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 83, 83, 83, 83, 83, 83, 83, 83, 83, 999, 999, 83, 83, 83, 83, 83, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 83, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  },
    {
    name: 'fieldPotato_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 84, 84, 84, 84, 84, 84, 84, 84, 84, 999, 999, 84, 84, 84, 84, 84, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 84, 84, 84, 84, 999, 999, 84, 84, 84, 84, 84, 84, 84, 84, 84, 84, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  },{
    name: 'fieldCabage_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 999, 999, 85, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 85, 85, 85, 85, 85, 85,999, 999, 85, 85, 85, 85, 85, 85, 85, 85, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  },{
    name: 'fieldOnion_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86,999, 999, 86, 86, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 86, 86, 86, 86, 999, 999, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 86, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }, 
  {
    name: 'fieldPepper_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 87, 87, 87, 87, 87, 87, 999, 999, 87, 87, 87, 87, 87, 87, 87, 87, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 999, 999, 87, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87, 87,999, 999, 87, 87, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  },  {
    name: 'fieldTomatoe_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 88, 88, 88, 88, 999, 999, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 999, 999, 88, 88, 88, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 88, 88, 88, 88, 999, 999, 88, 88, 88, 88, 88, 88, 88, 88, 88, 88, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }, {
    name: 'fieldBeens_1',
    width: 19,
  height: 14,
  tokenMap: [
    [32, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 34, 999, 10, 10, 10, 10, 999],
    [999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 32],
    [999, 89, 999, 999, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 10, 59],
    [10, 32, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 999, 32, 999, 999, 999, 10, 32],
    [10, 32, 999, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 10, 104],
    [10, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 10, 32],
    [10, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 37, 999, 999, 999, 999, 10, 32],
    [10, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 10, 999],
    [10, 89, 89, 89, 89, 89, 89, 89, 999, 999, 89, 89, 89, 89, 89, 89, 89, 10, 999],
    [10, 32, 999, 999, 999, 32, 999, 32, 68, 999, 999, 999, 999, 999, 999, 32, 999, 10, 999],
    [10, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 32, 999, 37, 999, 32, 999, 999],
    [10, 89, 89,999, 999, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 89, 999, 999],
    [10, 999, 999, 999, 32, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999],
    [10, 10, 10, 10, 999, 999, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  },
  
  
];