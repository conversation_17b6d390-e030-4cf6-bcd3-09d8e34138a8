{"name": "nuclearstory-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "start-watch": "vite", "dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-textures": "node scripts/generateTexturePaths.cjs"}, "dependencies": {"axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^4.5.0"}}