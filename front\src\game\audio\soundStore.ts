import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { SoundChannel, SoundSettings } from './channels'

interface SoundStoreState extends SoundSettings {
  // Методы для изменения настроек
  setVolume: (channel: SoundChannel, volume: number) => void
  setMuted: (channel: SoundChannel, muted: boolean) => void
  setGlobalMute: (muted: boolean) => void
  resetToDefaults: () => void
  
  // Вычисляемые значения
  getEffectiveVolume: (channel: SoundChannel) => number
}

const defaultSettings: SoundSettings = {
  volumes: {
    [SoundChannel.MASTER]: 70,
    [SoundChannel.EFFECTS]: 70,
    [SoundChannel.AMBIENT]: 70,
    [SoundChannel.DIALOG]: 70,
    [SoundChannel.UI]: 80
  },
  muted: {
    [SoundChannel.MASTER]: false,
    [SoundChannel.EFFECTS]: false,
    [SoundChannel.AMBIENT]: false,
    [SoundChannel.DIALOG]: false,
    [SoundChannel.UI]: false
  },
  globalMute: false
}

export const useSoundStore = create<SoundStoreState>()(
  persist(
    (set, get) => ({
      ...defaultSettings,
      
      setVolume: (channel: SoundChannel, volume: number) =>
        set((state) => ({
          volumes: { ...state.volumes, [channel]: Math.max(0, Math.min(100, volume)) }
        })),
        
      setMuted: (channel: SoundChannel, muted: boolean) =>
        set((state) => ({
          muted: { ...state.muted, [channel]: muted }
        })),
        
      setGlobalMute: (muted: boolean) =>
        set({ globalMute: muted }),
        
      resetToDefaults: () =>
        set(defaultSettings),
        
      getEffectiveVolume: (channel: SoundChannel) => {
        const state = get()
        if (state.globalMute || state.muted[channel]) {
          return 0
        }
        
        const masterVolume = state.volumes[SoundChannel.MASTER] / 100
        const channelVolume = state.volumes[channel] / 100
        
        return masterVolume * channelVolume
      }
    }),
    {
      name: 'nuclear-story-sound-settings',
      version: 1
    }
  )
)
