import { PresetLocationMap } from './presets/presetType';
import { MaterialTexture, TerrainType } from '../../shared/enums';

// Token constants для огородов (из tokenLegend)
const NONE = 999;
const FENCE = 10; // забор по периметру поля
// Остальные токены определены в массивах CROP_TOKENS и FIELD_DECORATIONS

// Культуры с весами для генерации полей
const CROP_TOKENS = [
  // Основные культуры (70% от всех растений)
  { token: 83, weight: 15, name: 'corn' },      // кукуруза
  { token: 84, weight: 12, name: 'potato' },    // картофель  
  { token: 88, weight: 10, name: 'tomato' },    // помидоры
  { token: 85, weight: 8, name: 'cabbage' },    // капуста
  { token: 86, weight: 8, name: 'onion' },      // лук
  
  // Дополнительные культуры (25% от всех растений)
  { token: 87, weight: 6, name: 'pepper' },     // перец
  { token: 89, weight: 4, name: 'beens' },      // бобы
  { token: 133, weight: 3, name: 'hop' },       // хмель
  { token: 135, weight: 2, name: 'wheat' },     // пшеница
  
  // Засохшие культуры (5% от всех растений) 
  { token: 134, weight: 3, name: 'witheredCrop' } // засохшие культуры
];

// Декоративные элементы для полей с весами
const FIELD_DECORATIONS = [
  // Основные элементы (60% декораций)
  { token: 32, weight: 20 }, // мусор/отходы
  { token: 1, weight: 15 },  // камни
  { token: 37, weight: 10 }, // случайные предметы
  
  // Контейнеры и инструменты (30% декораций)
  { token: 34, weight: 8 },  // ящики для урожая
  { token: 29, weight: 6 },  // бочки для воды
  { token: 33, weight: 4 },  // покрышки
  { token: 59, weight: 3 },  // поддоны
  
  // Редкие элементы (10% декораций)
  { token: 42, weight: 2 },  // скелет (заброшенность)
  { token: 116, weight: 3 }  // пугало (если есть в токенах)
];

// Построение весовых массивов
let WEIGHTED_CROPS: number[] = [];
let WEIGHTED_DECORATIONS: number[] = [];

function initWeightedArrays() {
  if (WEIGHTED_CROPS.length > 0) return;
  
  // Инициализация весов для культур
  for (const item of CROP_TOKENS) {
    for (let i = 0; i < item.weight; i++) {
      WEIGHTED_CROPS.push(item.token);
    }
  }
  
  // Инициализация весов для декораций
  for (const item of FIELD_DECORATIONS) {
    for (let i = 0; i < item.weight; i++) {
      WEIGHTED_DECORATIONS.push(item.token);
    }
  }
}

type RNG = () => number;
function defaultRng(): number { return Math.random(); }

interface GenerateFieldOptions {
  rng?: RNG;
  cropDensity?: number;        // плотность посадки культур (0.6-0.9)
  decorationDensity?: number;  // плотность декораций (0.1-0.3)  
  hasFence?: boolean;         // есть ли забор по периметру
  fenceCondition?: number;    // состояние забора (0-1, где 0 = разрушен)
  cropVariety?: number;       // разнообразие культур (1-3 типа на поле)
  witheredChance?: number;    // шанс засохших участков (0-0.4)
}

/**
 * Генерирует процедурный пресет поля заданных размеров
 * 1) Инициализирует все клетки как NONE
 * 2) Строит забор по периметру (если нужен)
 * 3) Заполняет внутреннюю часть культурами с учетом плотности
 * 4) Добавляет декоративные элементы
 */
export function generateFieldPreset(width: number, height: number, options: GenerateFieldOptions = {}): PresetLocationMap {
  initWeightedArrays();
  
  const rng = options.rng || defaultRng;
  const cropDensity = options.cropDensity ?? 0.75;
  const decorationDensity = options.decorationDensity ?? 0.15;
  const hasFence = options.hasFence ?? true;
  const fenceCondition = options.fenceCondition ?? 0.7;
  const cropVariety = options.cropVariety ?? 2;
  const witheredChance = options.witheredChance ?? 0.15;
  
  // 1) Инициализация всех клеток как NONE
  const map: number[][] = new Array(height).fill(null).map(() => new Array(width).fill(NONE));
  
  // 2) Построение забора по периметру
  if (hasFence && width >= 3 && height >= 3) {
    // Верх и низ
    for (let x = 0; x < width; x++) {
      if (rng() < fenceCondition) map[0][x] = FENCE;
      if (rng() < fenceCondition) map[height - 1][x] = FENCE;
    }
    // Левый и правый край
    for (let y = 0; y < height; y++) {
      if (rng() < fenceCondition) map[y][0] = FENCE;  
      if (rng() < fenceCondition) map[y][width - 1] = FENCE;
    }
  }
  
  // 3) Выбор основных культур для поля (1-3 типа)
  const selectedCrops: number[] = [];
  const availableCrops = CROP_TOKENS.filter(c => c.name !== 'witheredCrop'); // исключаем засохшие
  
  for (let i = 0; i < cropVariety; i++) {
    const crop = availableCrops[Math.floor(rng() * availableCrops.length)];
    if (!selectedCrops.includes(crop.token)) {
      selectedCrops.push(crop.token);
    }
  }
  
  // 4) Заполнение внутренней части культурами
  const innerStartX = hasFence ? 1 : 0;
  const innerEndX = hasFence ? width - 1 : width;
  const innerStartY = hasFence ? 1 : 0;
  const innerEndY = hasFence ? height - 1 : height;
  
  for (let y = innerStartY; y < innerEndY; y++) {
    for (let x = innerStartX; x < innerEndX; x++) {
      if (map[y][x] !== NONE) continue;
      
      if (rng() < cropDensity) {
        // Выбираем культуру из отобранных для этого поля
        const cropToken = selectedCrops[Math.floor(rng() * selectedCrops.length)];
        
        // С шансом заменяем на засохшую культуру
        if (rng() < witheredChance) {
          map[y][x] = 134; // witheredCrop
        } else {
          map[y][x] = cropToken;
        }
      }
    }
  }
  
  // 5) Добавление декоративных элементов
  for (let y = innerStartY; y < innerEndY; y++) {
    for (let x = innerStartX; x < innerEndX; x++) {
      if (map[y][x] === NONE && rng() < decorationDensity) {
        const decorationToken = WEIGHTED_DECORATIONS[Math.floor(rng() * WEIGHTED_DECORATIONS.length)];
        map[y][x] = decorationToken;
      }
    }
  }
  
  // 6) Создание финального пресета
  const preset: PresetLocationMap = {
    name: `field_auto_${width}x${height}_${Math.floor(rng() * 1e6)}`,
    width,
    height,
    tokenMap: map.map(row => row.slice()),
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
  };
  
  return preset;
}

/**
 * Создает и применяет поле к локации с покраской пола
 */
export function createAndApplyField(
  location: any,
  x: number,
  y: number, 
  width: number,
  height: number,
  legend: Record<string, string | number>,
  options: GenerateFieldOptions = {}
): PresetLocationMap {
  // 1. Генерируем пресет поля
  const fieldPreset = generateFieldPreset(width, height, options);
  
  // 2. Применяем пресет к локации с покраской пола
  applyFieldWithFloorPainting(location, { x, y, preset: fieldPreset }, legend);
  
  return fieldPreset;
}

/**
 * Применяет поле к локации с покраской пола в TerrainType.GROUND
 */
export function applyFieldWithFloorPainting(
  location: any,
  fieldPlacement: { x: number; y: number; preset: PresetLocationMap },
  legend: Record<string, string | number>
): void {
  const { x: offsetX, y: offsetY, preset } = fieldPlacement;
  const [locationWidth, locationHeight] = location.locationSize || [100, 100];
  
  // Собираем сет занятых координат декорациями, чтобы не залезать на другие пресеты
  const occupied = buildDecorationsOccupancySet(location);
  
  // 1. Собираем точки под полем для покраски в GROUND
  const fieldFloorPoints: [number, number][] = [];
  for (let y = 0; y < preset.height; y++) {
    for (let x = 0; x < preset.width; x++) {
      const targetX = offsetX + x;
      const targetY = offsetY + y;
      
      if (targetX >= 0 && targetY >= 0 && targetX < locationWidth && targetY < locationHeight) {
        // Не перекрашиваем пол под чужими декорациями
        if (!occupied.has(`${targetX},${targetY}`)) {
          fieldFloorPoints.push([targetX, targetY]);
        }
      }
    }
  }
  
  // 2. Покрашиваем пол под полем в GROUND
  if (fieldFloorPoints.length > 0) {
    addFloorToLocation(location, TerrainType.GROUND, fieldFloorPoints);
  }
  
  // 3. Применяем декорации поля
  applyFieldDecorations(location, fieldPlacement, legend, occupied);
}

/**
 * Применяет декорации поля к локации (без покраски пола)
 */
function applyFieldDecorations(
  location: any,
  fieldPlacement: { x: number; y: number; preset: PresetLocationMap },
  legend: Record<string, string | number>,
  occupied: Set<string>
): void {
  const { x: offsetX, y: offsetY, preset } = fieldPlacement;
  
  if (!location.decorations) location.decorations = {};

  // Создаем обратное отображение: токен -> название декорации
  const tokenToDecoration: Record<number, string> = {};
  for (const [decorationName, token] of Object.entries(legend)) {
    tokenToDecoration[token as number] = decorationName;
  }

  // Применяем токены пресета
  for (let y = 0; y < preset.height; y++) {
    for (let x = 0; x < preset.width; x++) {
      const token = preset.tokenMap[y] && preset.tokenMap[y][x];
      
      if (!token || token === 999) continue;
      
      const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());
      if (isNaN(tokenNum)) continue;
      
      const targetX = offsetX + x;
      const targetY = offsetY + y;
      
      const decorationName = tokenToDecoration[tokenNum];
      if (decorationName && decorationName !== 'none') {
        // Проверка коллизии: не размещаем поверх уже существующих декораций
        const key = `${targetX},${targetY}`;
        if (!occupied.has(key)) {
          if (!location.decorations[decorationName]) location.decorations[decorationName] = [];
          location.decorations[decorationName].push([targetX, targetY]);
          // Помечаем клетку как занятую, чтобы следующие токены не пересекались
          occupied.add(key);
        }
      }
    }
  }
}

// Собирает множество занятых координат текущими декорациями локации
function buildDecorationsOccupancySet(location: any): Set<string> {
  const set = new Set<string>();
  if (!location || !location.decorations) return set;
  for (const arr of Object.values(location.decorations) as any[]) {
    if (!Array.isArray(arr)) continue;
    for (const p of arr) {
      if (Array.isArray(p) && p.length >= 2) {
        const [x, y] = p as [number, number];
        set.add(`${x},${y}`);
      }
    }
  }
  return set;
}

// Вспомогательная функция для добавления пола
function addFloorToLocation(location: any, terrainType: TerrainType, points: [number, number][]): void {
  if (!points || points.length === 0) return;
  
  // Гарантируем наличие контейнера floor
  if (!location.floor) {
    location.floor = {} as Record<TerrainType, [number, number][]>;
    for (const t of Object.values(TerrainType) as TerrainType[]) {
      location.floor[t] = [];
    }
  }

  // Убираем эти точки из других типов поверхности
  const pointsSet = new Set(points.map(p => `${p[0]},${p[1]}`));
  for (const otherTerrain of Object.values(TerrainType) as TerrainType[]) {
    if (otherTerrain === terrainType) continue;
    if (location.floor[otherTerrain]) {
      location.floor[otherTerrain] = location.floor[otherTerrain].filter(
        (p: [number, number]) => !pointsSet.has(`${p[0]},${p[1]}`)
      );
    }
  }

  // Добавляем новые точки
  if (!location.floor[terrainType]) {
    location.floor[terrainType] = [];
  }
  location.floor[terrainType].push(...points);
}

/**
 * Генерирует случайные размеры поля подходящие для локации
 */
export function generateRandomFieldSize(rng: RNG = defaultRng): { width: number; height: number } {
  // Размеры полей: от 12x10 до 20x16
  const width = 12 + Math.floor(rng() * 9);  // 12-20
  const height = 10 + Math.floor(rng() * 7); // 10-16
  
  return { width, height };
}

/**
 * Создает пресет поля с оптимальными настройками для деревни
 */
export function createVillageFieldPreset(width: number, height: number, rng: RNG = defaultRng): PresetLocationMap {
  return generateFieldPreset(width, height, {
    rng,
    cropDensity: 0.7 + rng() * 0.2,        // 70-90% заполнения
    decorationDensity: 0.1 + rng() * 0.15, // 10-25% декораций
    hasFence: rng() < 0.6,                  // 80% полей с забором
    fenceCondition: 0.5 + rng() * 0.3,     // 50-90% целостность забора
    cropVariety: 1 + Math.floor(rng() * 2), // 1-2 типа культур
    witheredChance: 0.1 + rng() * 0.1       // 10-30% засохших участков
  });
}

export default generateFieldPreset;

/*
Примеры использования:

1. Простое создание и применение поля:
```typescript
import { createAndApplyField } from './fieldRND';

// В деревне или на ферме
const fieldPreset = createAndApplyField(
  location,     // объект локации
  15, 20,      // позиция x, y
  16, 12,      // размеры width, height
  legend,      // словарь токенов
  {            // опции
    cropDensity: 0.8,
    hasFence: true,
    cropVariety: 2
  }
);
```

2. Применение готового пресета поля с покраской пола:
```typescript
import { applyFieldWithFloorPainting } from './fieldRND';

const fieldPreset = generateFieldPreset(18, 14);
applyFieldWithFloorPainting(location, {
  x: 10, y: 15, preset: fieldPreset
}, legend);
```

3. Генерация пресета для деревни:
```typescript
const { width, height } = generateRandomFieldSize(customRng);
const villageField = createVillageFieldPreset(width, height, customRng);
```
*/
