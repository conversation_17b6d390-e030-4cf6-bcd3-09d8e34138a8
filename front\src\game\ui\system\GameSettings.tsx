import React from 'react'
import { X, Volume2, Monitor, <PERSON>, <PERSON>, Save } from 'lucide-react'
import { useGameStore } from '../../store/gameStore'
import { useSoundManager, SoundChannel } from '../../audio'
import { WorldMap } from '../../../shared/types/World'
import styles from './GameSettings.module.css'

/**
 * Сохраняет текущий мир на сервер
 */
export const saveCurrentWorld = async (worldId: string, worldData: WorldMap): Promise<{ success: boolean; error?: string }> => {
  try {
    const { updateWorld } = await import('../../../api/worldsApi')
    const result = await updateWorld(worldId, worldData)
    
    if (result.success) {
      return { success: true }
    } else {
      return { success: false, error: result.error }
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

interface GameSettingsProps {
  onClose: () => void
}

const GameSettings: React.FC<GameSettingsProps> = ({ onClose }) => {
  const { currentWorld, setCurrentWorld } = useGameStore()
  const soundManager = useSoundManager()
  if (!currentWorld) return null
  const settings = currentWorld.settings

  // Маппинг для terminalTextSize <-> слайдер
  const terminalSizeToSlider = (val: number) => {
    if (val <= 10) return 0
    if (val <= 12) return 1
    if (val <= 14) return 2
    return 3
  }
  const sliderToTerminalSize = (val: number) => [10, 12, 14, 16][val] || 12

  // Обработчик изменения настроек
  const handleSettingChange = (key: string, value: any) => {
    // запрещённые поля не меняем
    if ([
      'seed', 'language', 'worldSize', 'difficulty', 'timeScale'
    ].includes(key)) return
    setCurrentWorld({
      ...currentWorld,
      settings: {
        ...settings,
        [key]: value
      }
    })
  }

  const handleSave = () => {
    // Применяем настройки дисплея
    applyDisplaySettings()
    // Применяем размер текста терминала
    const terminal = document.querySelector('.terminalContent');
    if (terminal) {
      const sizes = ['10px', '12px', '14px', '16px'];
      (terminal as HTMLElement).style.fontSize = sizes[terminalSizeToSlider(settings.terminalTextSize)] || '12px';
    }
    onClose()
  }

  const applyDisplaySettings = () => {
    // Применяем яркость и контрастность к игровому интерфейсу
    const gameArea = document.querySelector('.gameArea')
    if (gameArea) {
      (gameArea as HTMLElement).style.filter =
        `brightness(${settings.brightness / 100}) contrast(${settings.contrast / 100})`
    }
  }

  const resetToDefaults = () => {
    // Сброс игровых настроек
    setCurrentWorld({
      ...currentWorld,
      settings: {
        ...settings,
        autosave: 'everytime',
        showPath: true,
        brightness: 80,
        contrast: 100,
        terminalTextSize: 12,
        worldMapPlayerSpeed: 2
      }
    })
    
    // Сброс звуковых настроек
    soundManager.resetToDefaults()
  }

  
  return (
    <div className={styles.gameSettings}>
      <div className={styles.header}>
        <h2>Настройки игры</h2>
        <button className={styles.closeButton} onClick={onClose}>
          <X />
        </button>
      </div>

      <div className={styles.content}>
        {/* Аудио настройки */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            <Volume2 />
            Аудио
          </h3>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Общая громкость: {soundManager.volumes[SoundChannel.MASTER]}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              value={soundManager.volumes[SoundChannel.MASTER]}
              onChange={(e) => soundManager.setVolume(SoundChannel.MASTER, parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Атмосфера: {soundManager.volumes[SoundChannel.AMBIENT]}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              value={soundManager.volumes[SoundChannel.AMBIENT]}
              onChange={(e) => soundManager.setVolume(SoundChannel.AMBIENT, parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Звуковые эффекты: {soundManager.volumes[SoundChannel.EFFECTS]}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              value={soundManager.volumes[SoundChannel.EFFECTS]}
              onChange={(e) => soundManager.setVolume(SoundChannel.EFFECTS, parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Диалоги: {soundManager.volumes[SoundChannel.DIALOG]}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              value={soundManager.volumes[SoundChannel.DIALOG]}
              onChange={(e) => soundManager.setVolume(SoundChannel.DIALOG, parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Кнопки меню: {soundManager.volumes[SoundChannel.UI]}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              step="1"
              value={soundManager.volumes[SoundChannel.UI]}
              onChange={(e) => soundManager.setVolume(SoundChannel.UI, parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
        </div>

        {/* Видео настройки */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>
            <Monitor />
            Видео
          </h3>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              <Sun />
              Яркость: {settings.brightness}%
            </label>
            <input
              type="range"
              min="30"
              max="150"
              step="1"
              value={settings.brightness}
              onChange={(e) => handleSettingChange('brightness', parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              <Moon />
              Контрастность: {settings.contrast}%
            </label>
            <input
              type="range"
              min="50"
              max="200"
              step="1"
              value={settings.contrast}
              onChange={(e) => handleSettingChange('contrast', parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Размер текста терминала: {['Мелкий','Стандарт','Крупный','Огромный'][terminalSizeToSlider(settings.terminalTextSize)]}
            </label>
            <input
              type="range"
              min="0"
              max="3"
              step="1"
              value={terminalSizeToSlider(settings.terminalTextSize)}
              onChange={(e) => handleSettingChange('terminalTextSize', sliderToTerminalSize(parseInt(e.target.value)))}
              className={styles.slider}
            />
          </div>
        </div>

        {/* Прочее */}
        <div className={styles.section}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              checked={settings.showPath}
              onChange={e => handleSettingChange('showPath', e.target.checked)}
            />
            <span>Показывать путь</span>
          </label>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Скорость анимации движения: 
              {settings.worldMapPlayerSpeed === 1 && ' (быстро)'}
              {settings.worldMapPlayerSpeed === 2 && ' (выше среднего)'}
              {settings.worldMapPlayerSpeed === 3 && ' (средне)'}
              {settings.worldMapPlayerSpeed === 4 && ' (ниже среднего)'}
              {settings.worldMapPlayerSpeed === 5 && ' (медленно)'}
            </label>
            <input
              type="range"
              min="1"
              max="5"
              step="1"
              value={settings.worldMapPlayerSpeed}
              onChange={(e) => handleSettingChange('worldMapPlayerSpeed', parseInt(e.target.value))}
              className={styles.slider}
            />
          </div>
          <div className={styles.setting}>
            <label className={styles.settingLabel}>
              Автосохранение:
              <select
                value={settings.autosave}
                onChange={e => handleSettingChange('autosave', e.target.value)}
              >
                <option value="everytime">Всегда</option>
                <option value="when_rest">При отдыхе</option>
                <option value="on_exit">При выходе</option>
                <option value="forbidden">Запрещено (1 жизнь)</option>
              </select>
            </label>
          </div>
        </div>
      </div>

      <div className={styles.footer}>
        <button className={styles.resetButton} onClick={resetToDefaults}>
          Сбросить
        </button>
        <div className={styles.actionButtons}>
          <button className={styles.cancelButton} onClick={onClose}>
            Отмена
          </button>
          <button className={styles.saveButton} onClick={handleSave}>
            <Save />
            Сохранить
          </button>
        </div>
      </div>
    </div>
  )
}

export default GameSettings
