import { PresetLocationMap } from "../presetType";

export const smallHousePresets: PresetLocationMap[] = [
  {
    name: 'smallHouse_1',
    width: 10,
  height: 8,
  tokenMap:[
    [11, 11, 12, 11, 11, 11, 11, 12, 11, 11],
    [11, 26, 17, 53, 999, 11, 110, 110, 60, 11],
    [12, 23, 42, 999, 32, 11, 110, 110, 999, 12],
    [11, 22, 32, 21, 999, 15, 999, 21, 32, 11],
    [12, 999, 42, 14, 999, 11, 11, 11, 11, 11],
    [11, 24, 32, 14, 999, 11, 25, 27, 27, 11],
    [11, 999, 999, 999, 999, 15, 32, 21, 999, 11],
    [11, 12, 11, 15, 11, 11, 11, 11, 11, 11]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];