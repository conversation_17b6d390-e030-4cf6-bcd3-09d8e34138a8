/**
 * Текстуры местности для глобальной карты
 */

export const TERRAIN_TEXTURES = {

  water: [
    `/textures/worldMap/terrain/water_1.png`
  ],
  wasteland: [
    `/textures/worldMap/terrain/wasteland-1.png`,
    `/textures/worldMap/terrain/wasteland-2.png`,
    `/textures/worldMap/terrain/wasteland-3.png`,
    `/textures/worldMap/terrain/wasteland-4.png`
  ],
} as const
export const TERRAIN_LOCATIONS_TEXTURES = {
  beton: [
    `/textures/Location/terrain/beton-1.png`,
    `/textures/Location/terrain/beton-2.png`,
    `/textures/Location/terrain/beton-3.png`,    
    `/textures/Location/terrain/beton-4.png`
  ],
  ground: [
    `/textures/Location/terrain/ground-1.png`,
    `/textures/Location/terrain/ground-2.png`,
    `/textures/Location/terrain/ground-3.png`,    
    `/textures/Location/terrain/ground-4.png`
  ],
  wasteland: [
    `/textures/Location/terrain/wasteland-1.png`,
    `/textures/Location/terrain/wasteland-2.png`,
    `/textures/Location/terrain/wasteland-3.png`,    
    `/textures/Location/terrain/wasteland-4.png`
  ],
  asphalt: [
    `/textures/Location/terrain/asphalt-1.png`,
    `/textures/Location/terrain/asphalt-2.png`,
    `/textures/Location/terrain/asphalt-3.png`,    
    `/textures/Location/terrain/asphalt-4.png`
  ],
  tiles: [
    `/textures/Location/terrain/tile-1.png`,
    `/textures/Location/terrain/tile-2.png`,
    `/textures/Location/terrain/tile-3.png`,    
    `/textures/Location/terrain/tile-4.png`
  ],
  wood: [
    `/textures/Location/terrain/wood-1.png`,
    `/textures/Location/terrain/wood-2.png`,
    `/textures/Location/terrain/wood-3.png`,    
    `/textures/Location/terrain/wood-4.png`
  ]


}