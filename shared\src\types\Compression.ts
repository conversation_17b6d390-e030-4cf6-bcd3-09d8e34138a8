/**
 * Типы для работы со сжатием данных мира
 */

export type CompressionAlgorithm = 'gzip';

/**
 * Интерфейс для сжатых данных мира
 */
export interface CompressedWorldData {
  /** Сжатые данные в виде Uint8Array */
  compressedData: Uint8Array;
  /** Алгоритм сжатия */
  algorithm: CompressionAlgorithm;
  /** Размер оригинальных данных в байтах */
  originalSize: number;
  /** Размер сжатых данных в байтах */
  compressedSize: number;
  /** Коэффициент сжатия (originalSize / compressedSize) */
  compressionRatio: number;
}

/**
 * Метаданные сжатия для хранения в БД
 */
export interface WorldCompressionMeta {
  algorithm: CompressionAlgorithm;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  compressedAt: Date;
}

/**
 * Результат операции сжатия
 */
export interface CompressionResult {
  success: boolean;
  data?: CompressedWorldData;
  error?: string;
  timeTaken?: number;
}

/**
 * Результат операции распаковки
 */
export interface DecompressionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timeTaken?: number;
}

/**
 * Конфигурация для сжатия
 */
export interface CompressionConfig {
  algorithm: CompressionAlgorithm;
  level?: number; // Уровень сжатия (1-9 для gzip)
}
