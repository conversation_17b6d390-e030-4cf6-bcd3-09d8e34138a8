.bubble {
  position: absolute;
  max-width: 240px;
  background: rgba(0, 0, 0, 0.7);
  margin-bottom: 30px;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.3;
  box-shadow: 0 2px 10px rgba(0,0,0,0.4), 0 0 8px var(--shadow-primary);
  pointer-events: none;
  transition: opacity 120ms linear;
  text-shadow: 0 0 4px rgba(0,0,0,0.8);
  /* Optional animation */
  animation: bubble-pop 180ms ease-out;
}

@keyframes bubble-pop {
  0% { transform: scale(0.85); opacity: 0; }
  60% { transform: scale(1.03); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}
