import { LocationSubtype } from '../../shared/enums';
import { PresetLocationMap } from './presets/presetType';
import { LocationConfig } from './constants/locationConfig';
import { TerrainType, MaterialTexture } from '../../shared/enums';

// Определение типа LegendMap
type LegendMap = Record<string, string | number>;

// Интерфейс для координат
type Point = [number, number];

// Импорты всех пресетов зданий
import { smallHousePresets } from './presets/buildings/smallHouse';
import { smallHotelPresets } from './presets/buildings/smallHotel';
import { mediumHousePresets } from './presets/buildings/mediumHouse';
import { bigHousePresets } from './presets/buildings/bigHouse';
import { fieldPresets } from './presets/buildings/field';
import { firstAidPresets } from './presets/buildings/firstAid';
import { smallShopPresets } from './presets/buildings/smallShop';
import { barPresets } from './presets/buildings/bar';
import { barakPresets } from './presets/buildings/baraks';
import { gunShopPresets } from './presets/buildings/gunShop';
import { warehousePresets } from './presets/buildings/warehouse';
import { toiletPresets } from './presets/buildings/toilet';
import { parkPresets } from './presets/buildings/park';
import { parkingPresets } from './presets/buildings/parking';
import { blockedRuinsPresets } from './presets/buildings/blockedRuins';
import { generateRuinPreset, createAndApplyRuin, applyRuinWithFloorPainting } from './ruinRND';

// Интерфейс для конфигурации пресета с шансом и количеством
interface PresetWithChance {
  presets: PresetLocationMap[];
  chance: number; // шанс применения 0-100
  min: number; // минимальное количество на локации (min: 1 = required, игнорирует chance)
  max: number; // максимальное количество на локации
  preferredSides?: ('top' | 'bottom' | 'left' | 'right')[]; // предпочтительные стороны дорог
  requiresMirrorOnWest?: boolean; // требует зеркалирование на западной стороне
}

// Категории пресетов по ориентации входов
enum PresetCategory {
  SOUTH_ENTRANCE = 'south_entrance', // пресеты с входом на юг (нужна северная/западная сторона дороги)
  UNIVERSAL = 'universal' // универсальные пресеты (любая сторона)
}

// Паттерны размещения зданий
enum PlacementPattern {
  SPIRAL = 'spiral',     // по спирали от центра наружу
  ROAD = 'road'          // вдоль дорог (streetAreas)
}

// Конфигурация размещения зданий для каждого подтипа локации
const BUILDING_PLACEMENT_CONFIG: Partial<Record<LocationSubtype, {
  availablePresets: PresetWithChance[];
  placementPatterns: { pattern: PlacementPattern; chance: number }[];
  minDistance: number; // минимальное расстояние между зданиями
}>> = {
  [LocationSubtype.TOWN]: {
    availablePresets: [
      // Пресеты с входом на ЮГ - приоритет северной и западной стороне дорог
      { presets: smallHousePresets, chance: 60, min: 2, max: 7, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: mediumHousePresets, chance: 60, min: 1, max: 5, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: bigHousePresets, chance: 30, min: 1, max: 3, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: smallHotelPresets, chance: 40, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: firstAidPresets, chance: 70, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: smallShopPresets, chance: 80, min: 1, max: 2, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: barPresets, chance: 50, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: gunShopPresets, chance: 40, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: warehousePresets, chance: 30, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      // Универсальные пресеты - любая сторона дорог
      { presets: blockedRuinsPresets, chance: 40, min: 3, max: 6 },
      { presets: toiletPresets, chance: 60, min: 1, max: 1 },
      { presets: parkPresets, chance: 25, min: 1, max: 2 },
      { presets: parkingPresets, chance: 20, min: 1, max: 2 },
    ],
    placementPatterns: [
      // Город теперь использует только дорожный паттерн
      { pattern: PlacementPattern.ROAD, chance: 100 }
    ],
    minDistance: 3
  },

  [LocationSubtype.VILLAGE]: {
    availablePresets: [
      // Пресеты с входом на ЮГ
      { presets: smallHousePresets, chance: 90, min: 1, max: 3, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: mediumHousePresets, chance: 40, min: 0, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: firstAidPresets, chance: 30, min: 0, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: smallShopPresets, chance: 50, min: 1, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: barPresets, chance: 40, min: 0, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      { presets: warehousePresets, chance: 50, min: 0, max: 1, preferredSides: ['top', 'left'], requiresMirrorOnWest: true },
      // Универсальные пресеты
      { presets: fieldPresets, chance: 60, min: 0, max: 2 },
      { presets: toiletPresets, chance: 70, min: 0, max: 1 },
      { presets: blockedRuinsPresets, chance: 10, min: 1, max: 3 }
    ],
    placementPatterns: [
      // Деревня также использует дорожный паттерн
      { pattern: PlacementPattern.ROAD, chance: 100 }
    ],
    minDistance: 2
  },

  [LocationSubtype.CAMP]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 70, min: 1, max: 2 },      // 1-2 маленьких дома
      { presets: smallHotelPresets, chance: 80, min: 0, max: 1 },      // 0-1 отель
      { presets: barakPresets, chance: 60, min: 0, max: 2 },           // 0-2 барака
      { presets: firstAidPresets, chance: 80, min: 1, max: 1 },        // обязательно 1 медпункт
      { presets: smallShopPresets, chance: 40, min: 0, max: 1 },       // 0-1 магазин
      { presets: gunShopPresets, chance: 70, min: 0, max: 1 },         // 0-1 оружейный
      { presets: warehousePresets, chance: 60, min: 0, max: 1 }       // обязательно 1-2 туалета
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 2
  },

  [LocationSubtype.FARM]: {
    availablePresets: [
      { presets: smallHousePresets, chance: 60, min: 0, max: 1 },      // 0-1 маленький дом
      { presets: fieldPresets, chance: 90, min: 2, max: 4 },           // обязательно 2-4 поля
      { presets: warehousePresets, chance: 80, min: 1, max: 2 },       // обязательно 1-2 склада
      { presets: barPresets, chance: 20, min: 0, max: 1 }         // 0-1 туалет
    ],
    placementPatterns: [
      { pattern: PlacementPattern.SPIRAL, chance: 100 }
    ],
    minDistance: 1
  }
};

// Дефолтная конфигурация для неопределенных подтипов
const DEFAULT_PLACEMENT_CONFIG = {
  availablePresets: [],
  requiredBuildings: [],
  placementPatterns: [{ pattern: PlacementPattern.SPIRAL, chance: 100 }],
  minDistance: 2
};

// Интерфейс для занятой области
interface OccupiedArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Интерфейс для позиции здания
interface BuildingPosition {
  x: number;
  y: number;
  preset: PresetLocationMap;
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  isRuin?: boolean; // флаг для обозначения что это руина
}

/**
 * Генерирует случайные отступы для пресета (2-4 клетки в каждую сторону)
 */
function generateRandomMargins(rng: () => number): { top: number; right: number; bottom: number; left: number } {
  return {
    top: 1 + Math.floor(rng() * 3),     // 2-4
    right: 1 + Math.floor(rng() * 3),   // 2-4 
    bottom: 1 + Math.floor(rng() * 3),  // 2-4
    left: 1 + Math.floor(rng() * 3)     // 2-4
  };
}

/**
 * Функция зеркалирования пресета - применяет изометрическое зеркалирование (меняет X и Y местами)
 */
function shouldMirrorPreset(preset: PresetLocationMap, rng: () => number): boolean {
  return preset.mirror && rng() < 0.5;
}

/**
 * Применяет изометрическое зеркалирование - меняет X и Y координаты местами
 */
function applyIsometricMirror(preset: PresetLocationMap): PresetLocationMap {
  const newTokenMap: (string | number)[][] = [];

  // Транспонируем матрицу (меняем строки и столбцы местами)
  for (let x = 0; x < preset.width; x++) {
    newTokenMap[x] = [];
    for (let y = 0; y < preset.height; y++) {
      newTokenMap[x][y] = preset.tokenMap[y][x];
    }
  }

  return {
    ...preset,
    width: preset.height,  // меняем размеры местами
    height: preset.width,
    tokenMap: newTokenMap,
    name: `${preset.name}_isometric_mirror`
  };
}

/**
 * Определяет центр локации
 */
function getLocationCenter(width: number, height: number): { x: number; y: number } {
  return {
    x: Math.floor(width / 2),
    y: Math.floor(height / 2)
  };
}

/**
 * Проверяет коллизию между областями с учетом отступов зданий
 */
function checkCollisionWithMargins(
  area1: OccupiedArea,
  area2: OccupiedArea,
  margins1: { top: number; right: number; bottom: number; left: number },
  margins2: { top: number; right: number; bottom: number; left: number }
): boolean {
  // Расширяем первую область с учетом ее отступов
  const expandedArea1 = {
    x: area1.x - margins1.left,
    y: area1.y - margins1.top,
    width: area1.width + margins1.left + margins1.right,
    height: area1.height + margins1.top + margins1.bottom
  };

  // Расширяем вторую область с учетом ее отступов
  const expandedArea2 = {
    x: area2.x - margins2.left,
    y: area2.y - margins2.top,
    width: area2.width + margins2.left + margins2.right,
    height: area2.height + margins2.top + margins2.bottom
  };

  return !(
    expandedArea1.x + expandedArea1.width <= expandedArea2.x ||
    expandedArea2.x + expandedArea2.width <= expandedArea1.x ||
    expandedArea1.y + expandedArea1.height <= expandedArea2.y ||
    expandedArea2.y + expandedArea2.height <= expandedArea1.y
  );
}

/**
 * Проверяет, помещается ли здание в границы локации с учетом отступов
 */
function isWithinBoundsWithMargins(
  x: number,
  y: number,
  presetWidth: number,
  presetHeight: number,
  margins: { top: number; right: number; bottom: number; left: number },
  locationWidth: number,
  locationHeight: number,
  borderMargin: number = 5
): boolean {
  const totalWidth = margins.left + presetWidth + margins.right;
  const totalHeight = margins.top + presetHeight + margins.bottom;
  const adjustedX = x - margins.left;
  const adjustedY = y - margins.top;

  return adjustedX >= borderMargin &&
    adjustedY >= borderMargin &&
    adjustedX + totalWidth <= locationWidth - borderMargin &&
    adjustedY + totalHeight <= locationHeight - borderMargin;
}

/**
 * Генерирует позиции по спирали от центра наружу
 */
function generateSpiralPositions(centerX: number, centerY: number, maxRadius: number): Array<{ x: number, y: number }> {
  const positions: Array<{ x: number, y: number }> = [];

  for (let radius = 0; radius <= maxRadius; radius++) {
    if (radius === 0) {
      positions.push({ x: centerX, y: centerY });
      continue;
    }

    // Генерируем позиции по кругу
    const circumference = 8 * radius;
    for (let i = 0; i < circumference; i++) {
      const angle = (2 * Math.PI * i) / circumference;
      const x = Math.round(centerX + radius * Math.cos(angle));
      const y = Math.round(centerY + radius * Math.sin(angle));
      positions.push({ x, y });
    }
  }

  return positions;
}

/**
 * Генерирует позиции вдоль дорог (streetAreas), если они сохранены в location.streetAreas
 * Правила:
 *  - здания ставятся вплотную к краю дороги (снаружи), дорога не перекрывается
 *  - чередуем стороны улицы
 *  - между зданиями на одной стороне выдерживаем расстояние 3-4 клетки
 *  - начинаем от центра улицы (по центральной точке streetArea)
 */
interface RoadCandidate {
  x: number; // якорь вдоль улицы
  y: number; // якорь вдоль улицы
  side: 'top' | 'bottom' | 'left' | 'right';
  street: { x: number; y: number; width: number; height: number };
}

function generateRoadAlignedPositions(location: any, rng: () => number): RoadCandidate[] {
  const candidates: RoadCandidate[] = [];
  const streetAreas = Array.isArray(location.streetAreas) ? location.streetAreas : [];
  if (streetAreas.length === 0) return candidates;

  const [locW, locH] = location.locationSize || [0, 0];
  const cx = Math.floor(locW / 2);
  const cy = Math.floor(locH / 2);

  streetAreas.sort((a: any, b: any) => {
    const acx = a.x + a.width / 2; const acy = a.y + a.height / 2;
    const bcx = b.x + b.width / 2; const bcy = b.y + b.height / 2;
    const da = Math.abs(acx - cx) + Math.abs(acy - cy);
    const db = Math.abs(bcx - cx) + Math.abs(bcy - cy);
    return da - db;
  });

  const minGap = 3;
  const maxGap = 4;

  for (const street of streetAreas) {
    const horizontal = street.width >= street.height;
    if (horizontal) {
      const yTopLine = street.y - 1;
      const yBottomLine = street.y + street.height;
      if (yTopLine < 0 && yBottomLine >= locH) continue;
      const centerX = Math.floor(street.x + street.width / 2);
      for (const dir of [-1, 1]) {
        let cursor = centerX;
        while (cursor >= street.x && cursor <= street.x + street.width) {
          cursor += dir * (minGap + Math.floor(rng() * (maxGap - minGap + 1)));
          if (cursor < street.x || cursor > street.x + street.width) break;
          if (yTopLine >= 0) candidates.push({ x: cursor, y: yTopLine, side: 'top', street });
          if (yBottomLine < locH) candidates.push({ x: cursor, y: yBottomLine, side: 'bottom', street });
        }
      }
    } else {
      const xLeftLine = street.x - 1;
      const xRightLine = street.x + street.width;
      if (xLeftLine < 0 && xRightLine >= locW) continue;
      const centerY = Math.floor(street.y + street.height / 2);
      for (const dir of [-1, 1]) {
        let cursor = centerY;
        while (cursor >= street.y && cursor <= street.y + street.height) {
          cursor += dir * (minGap + Math.floor(rng() * (maxGap - minGap + 1)));
          if (cursor < street.y || cursor > street.y + street.height) break;
          if (xLeftLine >= 0) candidates.push({ x: xLeftLine, y: cursor, side: 'left', street });
          if (xRightLine < locW) candidates.push({ x: xRightLine, y: cursor, side: 'right', street });
        }
      }
    }
  }

  return candidates;
}

/**
 * Группирует кандидатов по приоритетам для размещения зданий
 */
function groupCandidatesByPriority(candidates: RoadCandidate[]): {
  priority: RoadCandidate[];  // north + west (для пресетов с входом на юг)
  secondary: RoadCandidate[]; // south + east (для универсальных)
} {
  const priority: RoadCandidate[] = [];
  const secondary: RoadCandidate[] = [];

  for (const candidate of candidates) {
    if (candidate.side === 'top' || candidate.side === 'left') {
      priority.push(candidate);
    } else {
      secondary.push(candidate);
    }
  }

  return { priority, secondary };
}

/**
 * Определяет нужно ли зеркалирование для пресета на данной стороне дороги
 */
function shouldMirrorForRoadPlacement(
  preset: PresetLocationMap, 
  side: 'top' | 'bottom' | 'left' | 'right',
  presetConfig: PresetWithChance
): boolean {
  // Если у пресета есть предпочтительные стороны и это западная сторона - зеркалируем
  if (presetConfig.preferredSides && presetConfig.requiresMirrorOnWest && side === 'left') {
    return true;
  }
  // Для остальных случаев не зеркалируем
  return false;
}
/**
 * Получает пресеты для размещения с учетом min/max количества
 */
function getPresetsToPlace(config: typeof BUILDING_PLACEMENT_CONFIG[LocationSubtype], totalBuildingSlots: number, rng: () => number): Array<{preset: PresetLocationMap, config: PresetWithChance}> {
  const presetsToPlace: Array<{preset: PresetLocationMap, config: PresetWithChance}> = [];
  let remainingSlots = totalBuildingSlots;

  // Сначала добавляем обязательные пресеты (где min > 0)
  for (const presetConfig of config.availablePresets) {
    if (presetConfig.min > 0) {
      // Размещаем min зданий ОБЯЗАТЕЛЬНО
      for (let i = 0; i < presetConfig.min && remainingSlots > 0; i++) {
        let preset: PresetLocationMap;
        if (presetConfig.presets === blockedRuinsPresets) {
          const width = 10 + Math.floor(rng() * (20 - 10 + 1)); // 10-20
          const height = 13 + Math.floor(rng() * (24 - 13 + 1)); // 13-24
          preset = generateRuinPreset(width, height, { rng });
        } else {
          preset = presetConfig.presets[Math.floor(rng() * presetConfig.presets.length)];
        }
        presetsToPlace.push({preset, config: presetConfig});
        remainingSlots--;
      }

      // Затем пытаемся разместить дополнительные здания от min+1 до max с учетом chance
      const additionalCount = presetConfig.max - presetConfig.min;
      for (let i = 0; i < additionalCount && remainingSlots > 0; i++) {
        if (rng() * 100 < presetConfig.chance) {
          let preset: PresetLocationMap;
          if (presetConfig.presets === blockedRuinsPresets) {
            const width = 10 + Math.floor(rng() * (20 - 10 + 1)); // 10-20
            const height = 13 + Math.floor(rng() * (24 - 13 + 1)); // 13-24
            preset = generateRuinPreset(width, height, { rng });
          } else {
            preset = presetConfig.presets[Math.floor(rng() * presetConfig.presets.length)];
          }
          presetsToPlace.push({preset, config: presetConfig});
          remainingSlots--;
        }
      }
    }
  }

  // Затем заполняем оставшиеся слоты необязательными пресетами (где min = 0)
  while (remainingSlots > 0) {
    const availableOptional = config.availablePresets.filter(p => p.min === 0);
    if (availableOptional.length === 0) break;

    let placed = false;
    for (const presetConfig of availableOptional) {
      if (rng() * 100 < presetConfig.chance) {
        // Проверяем, не превышаем ли max для этого типа
        const currentCount = presetsToPlace.filter(p =>
          presetConfig.presets.some(configPreset => configPreset.name === p.preset.name)
        ).length;

        if (currentCount < presetConfig.max) {
          let preset: PresetLocationMap;
          if (presetConfig.presets === blockedRuinsPresets) {
            const width = 10 + Math.floor(rng() * (20 - 10 + 1));
            const height = 13 + Math.floor(rng() * (24 - 13 + 1));
            preset = generateRuinPreset(width, height, { rng });
          } else {
            preset = presetConfig.presets[Math.floor(rng() * presetConfig.presets.length)];
          }
          presetsToPlace.push({preset, config: presetConfig});
          remainingSlots--;
          placed = true;
          break;
        }
      }
    }

    if (!placed) break; // если ничего не удалось разместить, выходим
  }

  return presetsToPlace;
}

/**
 * Выбирает паттерн размещения с учетом шанса
 */
function selectPlacementPattern(patterns: { pattern: PlacementPattern; chance: number }[], rng: () => number): PlacementPattern {
  const random = rng() * 100;
  let cumulative = 0;

  for (const patternConfig of patterns) {
    cumulative += patternConfig.chance;
    if (random <= cumulative) {
      return patternConfig.pattern;
    }
  }

  return patterns[0].pattern; // fallback
}

/**
 * Главная функция размещения пресетов зданий для комплексных локаций
 */
export async function applyBuildingsPresets(
  location: any,
  config: LocationConfig,
  subtype: LocationSubtype,
  rng: () => number,
  legend: LegendMap
): Promise<void> {
  const placementConfig = BUILDING_PLACEMENT_CONFIG[subtype] || DEFAULT_PLACEMENT_CONFIG;

  if (!placementConfig || placementConfig.availablePresets.length === 0) {
    return;
  }

  const [locationWidth, locationHeight] = location.locationSize;
  const center = getLocationCenter(locationWidth, locationHeight);

  // Определяем количество зданий из LocationConfig
  const buildingCount = config.buildings
    ? config.buildings.min + Math.floor(rng() * (config.buildings.max - config.buildings.min + 1))
    : 3;


  // Выбираем паттерн размещения
  const selectedPattern = selectPlacementPattern(placementConfig.placementPatterns, rng);

  // Генерируем кандидатов (структура зависит от паттерна)
  let spiralCandidates: Array<{ x: number, y: number }> = [];
  let roadCandidates: RoadCandidate[] = [];
  if (selectedPattern === PlacementPattern.SPIRAL) {
    spiralCandidates = generateSpiralPositions(center.x, center.y, Math.min(locationWidth, locationHeight) / 4);
  } else if (selectedPattern === PlacementPattern.ROAD) {
    roadCandidates = generateRoadAlignedPositions(location, rng);
    // Приоритет центральных кандидатов (городское ядро) — сортируем по дистанции к центру
    roadCandidates.sort((a, b) => {
      const acx = a.x; const acy = a.y; // якорь
      const bcx = b.x; const bcy = b.y;
      const da = Math.abs(acx - center.x) + Math.abs(acy - center.y);
      const db = Math.abs(bcx - center.x) + Math.abs(bcy - center.y);
      return da - db;
    });
  }

  // Массив занятых областей для проверки коллизий
  const occupiedAreas: OccupiedArea[] = [];
  const placedBuildings: BuildingPosition[] = [];

      // Получаем пресеты для размещения с учетом min/max
  const buildingsToPlace = getPresetsToPlace(placementConfig, buildingCount, rng);


  const streetAreas = (location as any).streetAreas || [];

  function intersectsStreet(x: number, y: number, w: number, h: number): boolean {
    for (const s of streetAreas) {
      if (!(x + w <= s.x || x >= s.x + s.width || y + h <= s.y || y >= s.y + s.height)) return true;
    }
    return false;
  }

  // Размещаем здания
  for (const buildingData of buildingsToPlace) {
    const { preset, config: presetConfig } = buildingData;
    
    // Определяем, является ли это руиной
    const isRuin = presetConfig.presets === blockedRuinsPresets || 
                   preset.name?.includes('ruin') || 
                   preset.name?.includes('Ruin') || 
                   preset.name?.includes('RUIN');
    
    // Определяем нужно ли зеркалирование и применяем его
    let finalPreset = preset;
    let needsMirror = false;    if (selectedPattern === PlacementPattern.ROAD) {
      // Группируем кандидатов по приоритету
      const { priority, secondary } = groupCandidatesByPriority(roadCandidates);
      
      // Определяем какие кандидаты использовать для этого пресета
      const candidatesToUse = presetConfig.preferredSides ? priority : secondary;
      
      let placed = false;
      const margins = generateRandomMargins(rng);

      for (const cand of candidatesToUse) {
        // Определяем нужно ли зеркалирование для этой стороны
        needsMirror = shouldMirrorForRoadPlacement(preset, cand.side, presetConfig);
        finalPreset = needsMirror ? applyIsometricMirror(preset) : preset;

        // Сторонозависимая коррекция: строим вплотную без зазора между фасадом и краем улицы
        const m = { ...margins } as any;
        let topLeftX: number;
        let topLeftY: number;
        
        if (cand.side === 'top') {
          m.top = 0; m.bottom = 0;
          topLeftX = cand.x - Math.floor(finalPreset.width / 2);
          topLeftY = cand.y - (finalPreset.height - 1) - 1;
        } else if (cand.side === 'bottom') {
          m.top = 0; m.bottom = 0;
          topLeftX = cand.x - Math.floor(finalPreset.width / 2);
          topLeftY = cand.y;
        } else if (cand.side === 'left') {
          m.left = 0; m.right = 0;
          topLeftX = cand.x - (finalPreset.width - 1) - 1;
          topLeftY = cand.y - Math.floor(finalPreset.height / 2);
        } else { // right
          m.left = 0; m.right = 0;
          topLeftX = cand.x;
          topLeftY = cand.y - Math.floor(finalPreset.height / 2);
        }

        // Корректируем границы карты
        if (topLeftX < 0) topLeftX = 0;
        if (topLeftY < 0) topLeftY = 0;
        if (topLeftX + finalPreset.width > locationWidth) topLeftX = locationWidth - finalPreset.width;
        if (topLeftY + finalPreset.height > locationHeight) topLeftY = locationHeight - finalPreset.height;

        // Проверка пересечения с улицей (запрещено)
        if (intersectsStreet(topLeftX, topLeftY, finalPreset.width, finalPreset.height)) continue;

        // Проверка границ с отступами
        if (!isWithinBoundsWithMargins(topLeftX, topLeftY, finalPreset.width, finalPreset.height, m, locationWidth, locationHeight, 1)) continue;

        const area: OccupiedArea = { x: topLeftX, y: topLeftY, width: finalPreset.width, height: finalPreset.height };
        const hasCollision = placedBuildings.some(pb => {
          const placedArea: OccupiedArea = { x: pb.x, y: pb.y, width: pb.preset.width, height: pb.preset.height };
          return checkCollisionWithMargins(area, placedArea, m, pb.margins);
        });
        if (hasCollision) continue;

        occupiedAreas.push(area);
        placedBuildings.push({ x: topLeftX, y: topLeftY, preset: finalPreset, margins: m, isRuin });
        placed = true;
        break;
      }
      
      // Если не удалось разместить на приоритетных местах, пробуем вторичные
      if (!placed && presetConfig.preferredSides) {
        const margins = generateRandomMargins(rng);
        for (const cand of secondary) {
          needsMirror = shouldMirrorForRoadPlacement(preset, cand.side, presetConfig);
          finalPreset = needsMirror ? applyIsometricMirror(preset) : preset;

          const m = { ...margins } as any;
          let topLeftX: number;
          let topLeftY: number;
          
          if (cand.side === 'top') {
            m.top = 0; m.bottom = 0;
            topLeftX = cand.x - Math.floor(finalPreset.width / 2);
            topLeftY = cand.y - (finalPreset.height - 1) - 1;
          } else if (cand.side === 'bottom') {
            m.top = 0; m.bottom = 0;
            topLeftX = cand.x - Math.floor(finalPreset.width / 2);
            topLeftY = cand.y;
          } else if (cand.side === 'left') {
            m.left = 0; m.right = 0;
            topLeftX = cand.x - (finalPreset.width - 1) - 1;
            topLeftY = cand.y - Math.floor(finalPreset.height / 2);
          } else { // right
            m.left = 0; m.right = 0;
            topLeftX = cand.x;
            topLeftY = cand.y - Math.floor(finalPreset.height / 2);
          }

          // Корректируем границы карты
          if (topLeftX < 0) topLeftX = 0;
          if (topLeftY < 0) topLeftY = 0;
          if (topLeftX + finalPreset.width > locationWidth) topLeftX = locationWidth - finalPreset.width;
          if (topLeftY + finalPreset.height > locationHeight) topLeftY = locationHeight - finalPreset.height;

          // Проверка пересечения с улицей (запрещено)
          if (intersectsStreet(topLeftX, topLeftY, finalPreset.width, finalPreset.height)) continue;

          // Проверка границ с отступами
          if (!isWithinBoundsWithMargins(topLeftX, topLeftY, finalPreset.width, finalPreset.height, m, locationWidth, locationHeight, 1)) continue;

          const area: OccupiedArea = { x: topLeftX, y: topLeftY, width: finalPreset.width, height: finalPreset.height };
          const hasCollision = placedBuildings.some(pb => {
            const placedArea: OccupiedArea = { x: pb.x, y: pb.y, width: pb.preset.width, height: pb.preset.height };
            return checkCollisionWithMargins(area, placedArea, m, pb.margins);
          });
          if (hasCollision) continue;

          occupiedAreas.push(area);
          placedBuildings.push({ x: topLeftX, y: topLeftY, preset: finalPreset, margins: m, isRuin });
          break;
        }
      }
    } else {
      // Старый спиральный режим - используем старую логику зеркалирования
      finalPreset = shouldMirrorPreset(preset, rng) ? applyIsometricMirror(preset) : preset;
      const margins = generateRandomMargins(rng);
      
      for (const pos of spiralCandidates) {
        const area: OccupiedArea = { x: pos.x, y: pos.y, width: finalPreset.width, height: finalPreset.height };
        if (!isWithinBoundsWithMargins(pos.x, pos.y, finalPreset.width, finalPreset.height, margins, locationWidth, locationHeight)) continue;
        const hasCollision = placedBuildings.some(pb => {
          const placedArea: OccupiedArea = { x: pb.x, y: pb.y, width: pb.preset.width, height: pb.preset.height };
          return checkCollisionWithMargins(area, placedArea, margins, pb.margins);
        });
        if (hasCollision) continue;
        occupiedAreas.push(area);
        placedBuildings.push({ x: pos.x, y: pos.y, preset: finalPreset, margins, isRuin });
        break;
      }
    }
  }

  // Применяем пресеты к локации
  await applyPresetsToLocation(location, placedBuildings, legend);

  // Дополнительно заполняем оставшиеся места вдоль дорог руинами (не считается в лимите зданий)
  if (selectedPattern === PlacementPattern.ROAD) {
    await fillRemainingRoadSpaces(location, roadCandidates, placedBuildings, rng, legend);
  }

}

/**
 * Заполняет оставшиеся свободные места вдоль дорог руинами
 * Не считается в лимите основных зданий
 */
async function fillRemainingRoadSpaces(
  location: any,
  roadCandidates: RoadCandidate[],
  placedBuildings: BuildingPosition[],
  rng: () => number,
  legend: LegendMap
): Promise<void> {
  const [locationWidth, locationHeight] = location.locationSize;
  const streetAreas = (location as any).streetAreas || [];
  
  function intersectsStreet(x: number, y: number, w: number, h: number): boolean {
    for (const s of streetAreas) {
      if (!(x + w <= s.x || x >= s.x + s.width || y + h <= s.y || y >= s.y + s.height)) return true;
    }
    return false;
  }

  // Шанс размещения руин в свободных местах (50% чтобы не переполнить)
  const RUIN_FILL_CHANCE = 0.5;
  const additionalRuins: BuildingPosition[] = [];

  // Перебираем все кандидатов вдоль дорог
  for (const cand of roadCandidates) {
    if (rng() > RUIN_FILL_CHANCE) continue;

    // Генерируем случайные размеры руины
    let baseWidth = 8 + Math.floor(rng() * (15 - 8 + 1)); // 8-15
    let baseHeight = 10 + Math.floor(rng() * (18 - 10 + 1)); // 10-18
    
    // Размещаем длинной стороной к дороге
    let ruinWidth: number;
    let ruinHeight: number;
    let shouldRotate = false;
    
    if (cand.side === 'top' || cand.side === 'bottom') {
      // Горизонтальная дорога - длинную сторону располагаем горизонтально
      if (baseHeight > baseWidth) {
        // Поворачиваем на 90 градусов
        ruinWidth = baseHeight;
        ruinHeight = baseWidth;
        shouldRotate = true;
      } else {
        ruinWidth = baseWidth;
        ruinHeight = baseHeight;
      }
    } else { // left или right
      // Вертикальная дорога - длинную сторону располагаем вертикально  
      if (baseWidth > baseHeight) {
        // Поворачиваем на 90 градусов
        ruinWidth = baseHeight;
        ruinHeight = baseWidth;
        shouldRotate = true;
      } else {
        ruinWidth = baseWidth;
        ruinHeight = baseHeight;
      }
    }
    
    // Генерируем руину с оптимальными размерами
    let ruinPreset = generateRuinPreset(ruinWidth, ruinHeight, { rng });
    
    // Применяем зеркалирование для западной стороны (если нужно)
    if (cand.side === 'left') {
      ruinPreset = applyIsometricMirror(ruinPreset);
    }
    
    const margins = generateRandomMargins(rng);
    
    // Сторонозависимая коррекция позиций
    const m = { ...margins };
    let topLeftX: number;
    let topLeftY: number;
    
    if (cand.side === 'top') {
      m.top = 0; m.bottom = 0;
      topLeftX = cand.x - Math.floor(ruinPreset.width / 2);
      topLeftY = cand.y - (ruinPreset.height - 1) - 1;
    } else if (cand.side === 'bottom') {
      m.top = 0; m.bottom = 0;
      topLeftX = cand.x - Math.floor(ruinPreset.width / 2);
      topLeftY = cand.y;
    } else if (cand.side === 'left') {
      m.left = 0; m.right = 0;
      topLeftX = cand.x - (ruinPreset.width - 1) - 1;
      topLeftY = cand.y - Math.floor(ruinPreset.height / 2);
    } else { // right
      m.left = 0; m.right = 0;
      topLeftX = cand.x;
      topLeftY = cand.y - Math.floor(ruinPreset.height / 2);
    }

    // Корректируем границы карты
    if (topLeftX < 0) topLeftX = 0;
    if (topLeftY < 0) topLeftY = 0;
    if (topLeftX + ruinPreset.width > locationWidth) topLeftX = locationWidth - ruinPreset.width;
    if (topLeftY + ruinPreset.height > locationHeight) topLeftY = locationHeight - ruinPreset.height;

    // Проверка пересечения с улицей
    if (intersectsStreet(topLeftX, topLeftY, ruinPreset.width, ruinPreset.height)) continue;

    // Проверка границ с отступами
    if (!isWithinBoundsWithMargins(topLeftX, topLeftY, ruinPreset.width, ruinPreset.height, m, locationWidth, locationHeight, 1)) continue;

    // Проверка коллизий с уже размещенными зданиями
    const area: OccupiedArea = { x: topLeftX, y: topLeftY, width: ruinPreset.width, height: ruinPreset.height };
    const hasCollision = [...placedBuildings, ...additionalRuins].some(pb => {
      const placedArea: OccupiedArea = { x: pb.x, y: pb.y, width: pb.preset.width, height: pb.preset.height };
      return checkCollisionWithMargins(area, placedArea, m, pb.margins);
    });
    
    if (hasCollision) continue;

    // Размещаем руину
    additionalRuins.push({ x: topLeftX, y: topLeftY, preset: ruinPreset, margins: m, isRuin: true });
  }

  // Применяем дополнительные руины к локации
  if (additionalRuins.length > 0) {
    await applyPresetsToLocation(location, additionalRuins, legend);
  }
}

/**
 * Применяет размещенные пресеты к локации
 */
async function applyPresetsToLocation(
  location: any,
  buildings: BuildingPosition[],
  legend: LegendMap
): Promise<void> {
  if (!location.decorations) location.decorations = {};

  const tokenToDecoration: Record<number, string> = {};
  for (const [decorationName, token] of Object.entries(legend)) {
    tokenToDecoration[token as number] = decorationName;
  }

  const [locationWidth, locationHeight] = location.locationSize;

  for (const building of buildings) {
    const { x: offsetX, y: offsetY, preset, margins, isRuin } = building;

    // Очищаем область перед размещением с учетом отступов
    await clearBuildingAreaWithMargins(location, offsetX, offsetY, preset.width, preset.height, margins);

    // Если это руина - используем специальную функцию с покраской пола
    if (isRuin) {
      applyRuinWithFloorPainting(location, { x: offsetX, y: offsetY, preset }, legend);
      continue; // Пропускаем стандартную обработку, так как applyRuinWithFloorPainting уже всё сделала
    }

    // Применяем токены пресета (зеркалирование уже применено к самому пресету)
    for (let y = 0; y < preset.height; y++) {
      for (let x = 0; x < preset.width; x++) {
        const token = preset.tokenMap[y] && preset.tokenMap[y][x];

        if (!token || token === 999) continue;

        const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());
        if (isNaN(tokenNum)) continue;

        const targetX = offsetX + x;
        const targetY = offsetY + y;

        const decorationName = tokenToDecoration[tokenNum];
        if (decorationName && decorationName !== 'none') {
          if (!location.decorations[decorationName]) location.decorations[decorationName] = [];
          location.decorations[decorationName].push([targetX, targetY]);
        }
      }
    }
  }
}

/**
 * Очищает область перед размещением здания с учетом отступов
 */
async function clearBuildingAreaWithMargins(
  location: any,
  startX: number,
  startY: number,
  width: number,
  height: number,
  margins: { top: number; right: number; bottom: number; left: number }
): Promise<void> {
  if (!location.decorations) return;

  // Вычисляем расширенную область с отступами
  const expandedStartX = startX - margins.left;
  const expandedStartY = startY - margins.top;
  const expandedWidth = width + margins.left + margins.right;
  const expandedHeight = height + margins.top + margins.bottom;

  // Очищаем декорации в расширенной области
  for (const decorationName in location.decorations) {
    const decorations = location.decorations[decorationName];
    if (!Array.isArray(decorations)) continue;

    for (let i = decorations.length - 1; i >= 0; i--) {
      const [x, y] = decorations[i];
      if (x >= expandedStartX && x < expandedStartX + expandedWidth &&
        y >= expandedStartY && y < expandedStartY + expandedHeight) {
        decorations.splice(i, 1);
      }
    }

    // Удаляем пустые массивы
    if (decorations.length === 0) {
      delete location.decorations[decorationName];
    }
  }
}
