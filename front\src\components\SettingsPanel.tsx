import React, { useState } from 'react'
import { Volume2, VolumeX, Music, Speaker, Save, RotateCcw, User, Lock, Edit } from 'lucide-react'
import { useAuthStore } from '../store/authStore'
import { usersApi } from '../api/usersApi'
import styles from './SettingsPanel.module.css'
import { useSoundManager, SoundChannel } from '../game/audio'

type TabType = 'game' | 'account'

const SettingsPanel: React.FC = () => {
  const soundManager = useSoundManager()
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState<TabType>('game')
  const [accountForm, setAccountForm] = useState({
    username: user?.username || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const resetToDefaults = () => {
    if (confirm('Сбросить все настройки к значениям по умолчанию?')) {
      soundManager.resetToDefaults()
    }
  }

  const handleAccountFormChange = (field: keyof typeof accountForm, value: string) => {
    setAccountForm(prev => ({ ...prev, [field]: value }))
  }

  const handleUpdateProfile = async () => {
    try {
      // Check if username changed
      if (accountForm.username === user?.username) {
        alert('Нет изменений для сохранения')
        return
      }

      if (!accountForm.username.trim()) {
        alert('Имя пользователя не может быть пустым')
        return
      }

      await usersApi.updateProfile({ username: accountForm.username })
      
      // Update user in auth store
      const { updateUser } = useAuthStore.getState()
      updateUser({ username: accountForm.username })
      
      alert('Имя пользователя успешно обновлено!')
    } catch (error) {
      alert('Ошибка при обновлении профиля: ' + (error as Error).message)
    }
  }

  const handleChangePassword = async () => {
    if (accountForm.newPassword !== accountForm.confirmPassword) {
      alert('Новые пароли не совпадают')
      return
    }

    if (!accountForm.currentPassword || !accountForm.newPassword) {
      alert('Заполните все поля для смены пароля')
      return
    }

    try {
      await usersApi.changePassword({
        currentPassword: accountForm.currentPassword,
        newPassword: accountForm.newPassword
      })
      
      // Clear password fields
      setAccountForm(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }))
      
      alert('Пароль успешно изменен!')
    } catch (error) {
      alert('Ошибка при смене пароля: ' + (error as Error).message)
    }
  }

  return (
    <div className={styles.settingsPanel}>
      {/* Tabs */}
      <div className={styles.tabs}>
        <button
          onClick={() => setActiveTab('game')}
          className={`${styles.tab} ${activeTab === 'game' ? styles.tabActive : ''}`}
        >
          🎮 Игровые настройки
        </button>
        <button
          onClick={() => setActiveTab('account')}
          className={`${styles.tab} ${activeTab === 'account' ? styles.tabActive : ''}`}
        >
          👤 Аккаунт
        </button>
      </div>

      {/* Game Settings Tab */}
      {activeTab === 'game' && (
        <>
          {/* Audio Settings */}
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>
              🔊 Аудио настройки
            </h3>

            {/* Master Volume */}
            <div className={styles.setting}>
              <div className={styles.volumeContainer}>
                <label className={styles.labelText}>Общая громкость</label>
                <span className={styles.volumeValue}>{soundManager.volumes[SoundChannel.MASTER]}%</span>
              </div>
              <div className={styles.sliderContainer}>
                <VolumeX />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={soundManager.volumes[SoundChannel.MASTER]}
                  onChange={(e) => soundManager.setVolume(SoundChannel.MASTER, parseInt(e.target.value))}
                  className={styles.slider}
                />
                <Volume2 />
              </div>
            </div>

            {/* Effects Volume */}
            <div className={styles.setting}>
              <div className={styles.volumeContainer}>
                <label className={styles.labelText}>Звуковые эффекты</label>
                <span className={styles.volumeValue}>{soundManager.volumes[SoundChannel.EFFECTS]}%</span>
              </div>
              <div className={styles.sliderContainer}>
                <Speaker />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={soundManager.volumes[SoundChannel.EFFECTS]}
                  onChange={(e) => soundManager.setVolume(SoundChannel.EFFECTS, parseInt(e.target.value))}
                  className={styles.slider}
                />
                <Speaker />
              </div>
            </div>

            {/* Ambient Volume */}
            <div className={styles.setting}>
              <div className={styles.volumeContainer}>
                <label className={styles.labelText}>Атмосфера</label>
                <span className={styles.volumeValue}>{soundManager.volumes[SoundChannel.AMBIENT]}%</span>
              </div>
              <div className={styles.sliderContainer}>
                <Music />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={soundManager.volumes[SoundChannel.AMBIENT]}
                  onChange={(e) => soundManager.setVolume(SoundChannel.AMBIENT, parseInt(e.target.value))}
                  className={styles.slider}
                />
                <Music />
              </div>
            </div>

            {/* UI Volume */}
            <div className={styles.setting}>
              <div className={styles.volumeContainer}>
                <label className={styles.labelText}>Кнопки меню</label>
                <span className={styles.volumeValue}>{soundManager.volumes[SoundChannel.UI]}%</span>
              </div>
              <div className={styles.sliderContainer}>
                <Volume2 />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={soundManager.volumes[SoundChannel.UI]}
                  onChange={(e) => soundManager.setVolume(SoundChannel.UI, parseInt(e.target.value))}
                  className={styles.slider}
                />
                <Volume2 />
              </div>
            </div>

            {/* Global Mute Toggle */}
            <div className={styles.settingRow}>
              <div className={styles.settingLabel}>
                {soundManager.globalMute ? <VolumeX /> : <Volume2 />}
                <label className={styles.labelText}>Общее отключение звука</label>
              </div>
              <button
                onClick={() => soundManager.setGlobalMute(!soundManager.globalMute)}
                className={`${styles.toggle} ${!soundManager.globalMute ? styles.toggleOn : styles.toggleOff}`}
              >
                <span
                  className={`${styles.toggleThumb} ${!soundManager.globalMute ? styles.toggleThumbOn : ''}`}
                />
              </button>
            </div>
          </div>

         

          {/* Reset Button */}
          <div className={styles.resetSection}>
            <button
              onClick={resetToDefaults}
              className={styles.resetButton}
            >
              <RotateCcw />
              Сбросить к настройкам по умолчанию
            </button>
          </div>
        </>
      )}

      {/* Account Settings Tab */}
      {activeTab === 'account' && (
        <>
          {/* Profile Settings */}
          <div className={styles.profileSection}>
            <h3>
              👤 Профиль
            </h3>

            <div className={styles.inputGroup}>
              <label>Имя пользователя</label>
              <div className={styles.inputWrapper}>
                <User className={styles.inputIcon} />
                <input
                  type="text"
                  value={accountForm.username}
                  onChange={(e) => handleAccountFormChange('username', e.target.value)}
                  className={styles.input}
                  placeholder="Введите имя пользователя"
                />
              </div>
            </div>

            <button
              onClick={handleUpdateProfile}
              className={styles.saveButton}
            >
              <Edit />
              Обновить имя пользователя
            </button>
          </div>

          {/* Password Settings */}
          <div className={styles.passwordSection}>
            <h3>
              🔒 Безопасность
            </h3>

            <div className={styles.inputGroup}>
              <label>Текущий пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.currentPassword}
                  onChange={(e) => handleAccountFormChange('currentPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Введите текущий пароль"
                />
              </div>
            </div>

            <div className={styles.inputGroup}>
              <label>Новый пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.newPassword}
                  onChange={(e) => handleAccountFormChange('newPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Введите новый пароль"
                />
              </div>
            </div>

            <div className={styles.inputGroup}>
              <label>Подтвердите пароль</label>
              <div className={styles.inputWrapper}>
                <Lock className={styles.inputIcon} />
                <input
                  type="password"
                  value={accountForm.confirmPassword}
                  onChange={(e) => handleAccountFormChange('confirmPassword', e.target.value)}
                  className={styles.input}
                  placeholder="Подтвердите новый пароль"
                />
              </div>
            </div>

            <button
              onClick={handleChangePassword}
              className={styles.saveButton}
              disabled={!accountForm.currentPassword || !accountForm.newPassword || !accountForm.confirmPassword}
            >
              <Lock />
              Изменить пароль
            </button>
          </div>
        </>
      )}
    </div>
  )
}

export default SettingsPanel
