/**
 * Система отрисовки паттерновых текстур для рек
 */

import { WorldMapCell } from '../../../../shared/types/World';
import { WorldMapDecorations } from '../../../../shared/enums';
import { PatternTextureManager } from './basePatternTextureManager';

/**
 * Менеджер паттерновых текстур для рек
 */
class RiverTextureManager extends PatternTextureManager {
  /**
   * Получает текстуру реки на основе направлений и номера изображения
   * @param decorationBorder - массив направлений [1, 2] означает папку "1_2"
   * @param imgDirection - номер изображения от 1 до 4
   */
  async getRiverPatternTexture(decorationBorder: number[], imgDirection: number): Promise<HTMLImageElement | null> {
    // Для реки используем все направления без фильтрации (только 1-4, без углов)
    const filteredBorders = decorationBorder.filter(border => border >= 1 && border <= 4);
    
    // Если нет направлений, возвращаем null
    if (filteredBorders.length === 0) {
      return null;
    }

    // Формируем путь к текстуре
    const borderKey = filteredBorders.sort((a, b) => a - b).join('_');
    const texturePath = `/textures/worldMap/decorations/river/${borderKey}/${imgDirection}.png`;

    // Создаем промис загрузки через централизованный loader
    const loadingPromise = this.createLoadingPromise(texturePath, '');

    try {
      return await loadingPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * Синхронно получает текстуру реки (если она уже загружена)
   */
  getLoadedRiverTexture(decorationBorder: number[], imgDirection: number): HTMLImageElement | null {
    // Для реки используем все направления (только 1-4)
    const filteredBorders = decorationBorder.filter(border => border >= 1 && border <= 4);
    
    if (filteredBorders.length === 0) {
      return null;
    }

    const borderKey = filteredBorders.sort((a, b) => a - b).join('_');
    const texturePath = `/textures/worldMap/decorations/river/${borderKey}/${imgDirection}.png`;
    
    // Получаем из централизованного кэша
    return this.getLoadedTexture(texturePath) || null;
  }

  /**
   * Предзагружает все возможные комбинации текстур рек
   */
  async preloadAllRiverTextures(): Promise<void> {
    const borderCombinations = [
      [1], [2], [3], [4],
      [1, 2], [1, 3], [1, 4], [2, 3], [2, 4], [3, 4],
      [1, 2, 3], [2, 3, 4], [3, 4, 1], [1, 2, 4],
      [1, 2, 3, 4]
    ];

    const loadPromises: Promise<HTMLImageElement | null>[] = [];

    // Загружаем текстуры рек
    for (const borders of borderCombinations) {
      for (let direction = 1; direction <= 4; direction++) {
        loadPromises.push(this.getRiverPatternTexture(borders, direction));
      }
    }

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текстур рек
export const riverTextureManager = new RiverTextureManager();