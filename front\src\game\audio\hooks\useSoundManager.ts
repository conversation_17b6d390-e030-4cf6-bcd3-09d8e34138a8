import { useMemo } from 'react'
import { soundManager } from '../soundManager'
import { useSoundStore } from '../soundStore'
import { SoundChannel, PlaySoundOptions } from '../channels'

/**
 * Основной хук для работы со звуком
 */
export const useSoundManager = () => {
  const soundStore = useSoundStore()
  
  return useMemo(() => ({
    // Основные методы
    playSound: (path: string, channel: SoundChannel, options?: PlaySoundOptions, trackName?: string) =>
      soundManager.playSound(path, channel, options, trackName),
    stopSound: (id: string) => soundManager.stopSound(id),
    stopTrack: (trackName: string) => soundManager.stopTrack(trackName),
    stopChannel: (channel: SoundChannel) => soundManager.stopChannel(channel),
    stopAll: () => soundManager.stopAll(),
    
    // Проверки
    isTrackPlaying: (trackName: string) => soundManager.isTrackPlaying(trackName),
    getActiveSounds: () => soundManager.getActiveSounds(),
    
    // Настройки
    volumes: soundStore.volumes,
    muted: soundStore.muted,
    globalMute: soundStore.globalMute,
    
    // Методы изменения настроек
    setVolume: soundStore.setVolume,
    setMuted: soundStore.setMuted,
    setGlobalMute: soundStore.setGlobalMute,
    resetToDefaults: soundStore.resetToDefaults,
    getEffectiveVolume: soundStore.getEffectiveVolume
  }), [soundStore])
}

/**
 * Хук для быстрого воспроизведения UI звуков
 */
export const useUISound = () => {
  const { playSound } = useSoundManager()
  
  return useMemo(() => ({
    playClick: () => playSound('/sound/ui/click.ogg', SoundChannel.UI),
    playHover: () => playSound('/sound/ui/hover.ogg', SoundChannel.UI),
    playError: () => playSound('/sound/ui/error.ogg', SoundChannel.UI),
    playSuccess: () => playSound('/sound/ui/success.ogg', SoundChannel.UI)
  }), [playSound])
}
