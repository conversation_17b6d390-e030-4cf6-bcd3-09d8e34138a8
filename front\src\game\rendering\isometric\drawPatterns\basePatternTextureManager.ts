/**
 * Базовый класс для менеджеров паттерновых текстур
 */

import { textureLoader } from "../../textures/TextureLoader";

/**
 * Базовый менеджер паттерновых текстур - теперь использует централизованный кэш
 */
export abstract class PatternTextureManager {
  /**
   * Создает промис загрузки текстуры через централизованный loader
   * @param texturePath - путь к текстуре
   * @param textureKey - ключ для кэша (не используется, так как loader сам управляет ключами)
   */
  protected createLoadingPromise(texturePath: string, textureKey: string): Promise<HTMLImageElement> {
    // Делегируем все в centralizedный textureLoader
    return textureLoader.loadTexture(texturePath);
  }

  /**
   * Получает загруженную текстуру из централизованного кэша
   */
  protected getLoadedTexture(texturePath: string): HTMLImageElement | undefined {
    return textureLoader.getTexture(texturePath);
  }

  /**
   * Очищает кэш текстур (теперь это no-op, так как используется глобальный кэш)
   */
  clearCache(): void {
    // No-op: глобальный кэш управляется централизованно
  }
}