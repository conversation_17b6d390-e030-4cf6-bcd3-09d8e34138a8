/**
 * Система загрузки и управления текстурами для локаций
 */

import { TerrainType, LocationDecorations } from '../../../shared/enums';
import { TERRAIN_TEXTURES, TERRAIN_LOCATIONS_TEXTURES } from './terrain';
import { textureLoader } from './TextureLoader';


// Маппинг типов земли локации на текстуры (обновленный под TerrainType)
export const LOCATION_TERRAIN_TEXTURES: Record<TerrainType, readonly string[]> = {
  [TerrainType.ASPHALT]: TERRAIN_LOCATIONS_TEXTURES.asphalt, // нет отдельной текстуры, используем grass
  [TerrainType.BETON]: TERRAIN_LOCATIONS_TEXTURES.beton,
  [TerrainType.TILES]: TERRAIN_LOCATIONS_TEXTURES.tiles, // используем специальную текстуру бетона
  [TerrainType.WOOD]: TERRAIN_LOCATIONS_TEXTURES.wood, // нет отдельной текстуры, используем grass
  [TerrainType.METAL]: TERRAIN_LOCATIONS_TEXTURES.beton, // нет отдельной текстуры, используем grass
  [TerrainType.GROUND]: TERRAIN_LOCATIONS_TEXTURES.ground, // используем grass вместо несуществующего desert
  [TerrainType.WATER]: TERRAIN_TEXTURES.water,
  [TerrainType.WASTELAND]: TERRAIN_LOCATIONS_TEXTURES.wasteland
};

/**
 * Получает детерминированную текстуру для типа земли на основе координат
 */
export function getLocationTexture(terrain: TerrainType, x: number, y: number): string | null {
  const textures = LOCATION_TERRAIN_TEXTURES[terrain];
  if (!textures || textures.length === 0) {
    return null;
  }
  // Детерминированный выбор с использованием числа π для лучшего распределения
  const p = (x * Math.PI + y * Math.PI * 2.71828);
  const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
  const index = Math.floor(hash) % textures.length;
  return textures[index];
}

/**
 * Получает случайную текстуру для типа земли (устаревшая функция)
 * @deprecated Используйте getLocationTexture с координатами для стабильности
 */
export function getRandomLocationTexture(terrain: TerrainType): string | null {
  const textures = LOCATION_TERRAIN_TEXTURES[terrain];
  if (!textures || textures.length === 0) {
    return null;
  }
  
  const randomIndex = Math.floor(Math.random() * textures.length);
  return textures[randomIndex];
}

/**
 * Предзагружает все текстуры локаций
 */
export async function preloadLocationTextures(): Promise<void> {
  const allTextures: string[] = [];
  
  // Собираем все уникальные текстуры
  Object.values(LOCATION_TERRAIN_TEXTURES).forEach(textureArray => {
    allTextures.push(...textureArray);
  });
  
  // Убираем дубликаты
  const uniqueTextures = [...new Set(allTextures)];
  
  // Загружаем все текстуры параллельно
  const loadPromises = uniqueTextures.map(texture => textureLoader.loadTexture(texture));
  
  try {
    await Promise.all(loadPromises);
  } catch (error) {
    console.error('Ошибка при предзагрузке текстур локаций:', error);
  }
}

/**
 * Получает уже загруженную текстуру из кеша (если она есть)
 */
export function getCachedTexture(src: string): HTMLImageElement | undefined {
  return textureLoader.getTexture(src);
}

