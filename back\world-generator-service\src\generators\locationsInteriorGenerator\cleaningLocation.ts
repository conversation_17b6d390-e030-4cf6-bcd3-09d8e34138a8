import { TransferLocation, Point } from '../../shared/types/Location';
import { LocationDecorations, LocationType } from '../../shared/enums';

type CleaningPass = (location: TransferLocation) => Promise<void> | void;

// Registry for cleaning passes so user can add multiple runs
const cleaningPasses: Record<string, CleaningPass> = {};

/**
 * Register a named cleaning pass. You can add custom passes and then run them by name.
 */
export function registerCleaningPass(name: string, pass: CleaningPass) {
  if (!name || typeof pass !== 'function') return;
  cleaningPasses[name] = pass;
}

/**
 * Run registered cleaning passes on the given location.
 * If names array is provided, runs only those passes (in that order).
 * Otherwise runs all registered passes in registration order.
 */
export async function runCleaningPasses(location: TransferLocation, names?: string[]) {
  if (!location) return;

  const toRun = names && names.length
    ? names.map(n => cleaningPasses[n]).filter(Boolean)
    : Object.keys(cleaningPasses).map(k => cleaningPasses[k]);

  for (const p of toRun) {
    try {
      await p(location as TransferLocation);
    } catch (err) {
      // swallow - cleaning passes should be non-fatal
      // in future we can optionally collect diagnostics
    }
  }
}

/**
 * Built-in pass: ensure trees on OUTDOOR locations are not closer than minDistance.
 * Trees that violate spacing are removed and their positions are added to NONE.
 */
export async function enforceOutdoorTreeSpacing(location: TransferLocation, minDistance = 2) {
  if (!location) return;
  if (location.type !== LocationType.OUTDOOR) return;

  location.decorations = location.decorations || {};
  const decos = location.decorations as Partial<Record<string, Point[]>>;

  const treeKey = LocationDecorations.TREE as unknown as string;
  const noneKey = LocationDecorations.NONE as unknown as string;

  const trees = (decos[treeKey] || []).slice();
  if (!trees.length) return;


  function dist(a: Point, b: Point) {
    const dx = a[0] - b[0];
    const dy = a[1] - b[1];
    return Math.sqrt(dx * dx + dy * dy);
  }

  const remaining = trees.slice();
  const removed: Point[] = [];

  function findClosestPair(arr: Point[]) {
    let best = { i: -1, j: -1, d: Infinity };
    for (let i = 0; i < arr.length; i++) {
      for (let j = i + 1; j < arr.length; j++) {
        const d = dist(arr[i], arr[j]);
        if (d < best.d) best = { i, j, d };
      }
    }
    if (best.i === -1) return null;
    return best;
  }

  // Remove closest pairs until the closest distance is >= minDistance
  while (true) {
    const pair = findClosestPair(remaining);
    if (!pair || pair.d >= minDistance) break;

    // Choose which of the pair to remove: prefer to remove the point with
    // more neighbors within minDistance (to reduce future conflicts).
    const neighborCount = (idx: number) =>
      remaining.reduce((c, p, k) => (k !== idx && dist(p, remaining[idx]) < minDistance ? c + 1 : c), 0);

    const c0 = neighborCount(pair.i);
    const c1 = neighborCount(pair.j);
    const removeIndex = c0 > c1 ? pair.i : pair.j;

    const [removedPoint] = remaining.splice(removeIndex, 1);
    removed.push(removedPoint);
  }

  const keep = remaining;

  // write results back
  decos[treeKey] = keep;
  if (!decos[noneKey]) decos[noneKey] = [];
  decos[noneKey] = decos[noneKey].concat(removed);
}

// register the built-in pass under a clear name
// register default pass with a 4-tile minimum spacing
registerCleaningPass('outdoor-tree-spacing', (loc) => enforceOutdoorTreeSpacing(loc, 4));

/**
 * Очищает область вокруг дверей от декораций (кроме исключений)
 * Удаляет декорации в радиусе 1 клетки от дверей (по сторонам света, не по диагонали)
 */
export async function cleanAroundDoors(location: TransferLocation) {
  if (!location || !location.decorations) return;

  const decos = location.decorations as Partial<Record<string, Point[]>>;
  const doors = decos[LocationDecorations.DOOR] || [];
  
  if (!doors.length) return;

  // Декорации, которые НЕ удаляем возле дверей
  const protectedDecorations = new Set([
    LocationDecorations.WALL,
    LocationDecorations.WINDOW, 
    LocationDecorations.LITTER,
    LocationDecorations.PUDDLE,
    LocationDecorations.ROCKS,
    LocationDecorations.GRASS,
    LocationDecorations.CARPET
  ]);

  // Собираем все точки для удаления
  const pointsToRemove = new Map<string, Set<string>>(); // decorationType -> Set of "x,y"

  for (const [doorX, doorY] of doors) {
    // Проверяем 4 стороны (не по диагонали)
    const directions = [
      [doorX, doorY - 1], // север
      [doorX + 1, doorY], // восток  
      [doorX, doorY + 1], // юг
      [doorX - 1, doorY]  // запад
    ];

    for (const [checkX, checkY] of directions) {
      const pointKey = `${checkX},${checkY}`;
      
      // Проверяем все типы декораций
      for (const [decoType, points] of Object.entries(decos)) {
        if (protectedDecorations.has(decoType as any)) continue;
        if (!points || !Array.isArray(points)) continue;

        // Проверяем, есть ли декорация в этой точке
        const hasDecoration = points.some(([x, y]) => x === checkX && y === checkY);
        if (hasDecoration) {
          if (!pointsToRemove.has(decoType)) {
            pointsToRemove.set(decoType, new Set());
          }
          pointsToRemove.get(decoType)!.add(pointKey);
        }
      }
    }
  }

  // Удаляем найденные декорации
  for (const [decoType, pointsSet] of pointsToRemove) {
    const currentPoints = decos[decoType] || [];
    decos[decoType] = currentPoints.filter(([x, y]) => 
      !pointsSet.has(`${x},${y}`)
    );
  }
}

/**
 * Очищает область вокруг машин от декораций
 * Удаляет декорации на 1 тайл южнее, восточнее и юго-восточнее от каждой клетки машины
 */
export async function cleanAroundCars(location: TransferLocation) {
  if (!location || !location.decorations) return;

  const decos = location.decorations as Partial<Record<string, Point[]>>;
  const cars = decos[LocationDecorations.CAR] || [];
  
  if (!cars.length) return;

  // Декорации, которые НЕ удаляем возле машин
  const protectedDecorations = new Set([
    LocationDecorations.CAR,     // не удаляем саму машину
    LocationDecorations.LITTER,
    LocationDecorations.PUDDLE,
    LocationDecorations.ROCKS,
    LocationDecorations.GRASS,
    LocationDecorations.BUSH,
    LocationDecorations.UNIVERSALRND,
    LocationDecorations.SCELETON,
    LocationDecorations.ROAD,
    LocationDecorations.GASSTATIONPUMP
  ]);

  // Собираем все точки для удаления
  const pointsToRemove = new Map<string, Set<string>>(); // decorationType -> Set of "x,y"

  for (const [carX, carY] of cars) {
    // Проверяем 3 направления от каждой клетки машины:
    // 1 тайл южнее, 1 тайл восточнее, 1 тайл юго-восточнее
    const directions = [
      [carX, carY + 1],     // юг
      [carX + 1, carY],     // восток
      [carX + 1, carY + 1]  // юго-восток
    ];

    for (const [checkX, checkY] of directions) {
      const pointKey = `${checkX},${checkY}`;
      
      // Проверяем все типы декораций
      for (const [decoType, points] of Object.entries(decos)) {
        if (protectedDecorations.has(decoType as any)) continue;
        if (!points || !Array.isArray(points)) continue;

        // Проверяем, есть ли декорация в этой точке
        const hasDecoration = points.some(([x, y]) => x === checkX && y === checkY);
        if (hasDecoration) {
          if (!pointsToRemove.has(decoType)) {
            pointsToRemove.set(decoType, new Set());
          }
          pointsToRemove.get(decoType)!.add(pointKey);
        }
      }
    }
  }

  // Удаляем найденные декорации
  for (const [decoType, pointsSet] of pointsToRemove) {
    const currentPoints = decos[decoType] || [];
    decos[decoType] = currentPoints.filter(([x, y]) => 
      !pointsSet.has(`${x},${y}`)
    );
  }
}

/**
 * Заменяет здоровые культуры на засохшие с шансом 30%
 */
export async function witherCrops(location: TransferLocation) {
  if (!location || !location.decorations) return;

  const decos = location.decorations as Partial<Record<string, Point[]>>;
  
  // Типы здоровых культур, которые могут засохнуть
  const healthyCrops = [
    LocationDecorations.TOMATO,
    LocationDecorations.PEPPER,
    LocationDecorations.CORN,
    LocationDecorations.POTATO,
    LocationDecorations.BEENS,
    LocationDecorations.ONION,
    LocationDecorations.CABBAGE,
    LocationDecorations.WHEAT,
    LocationDecorations.HOP
  ];

  // Инициализируем массив засохших культур если его нет
  if (!decos[LocationDecorations.WITHEREDCROP]) {
    decos[LocationDecorations.WITHEREDCROP] = [];
  }

  // Обрабатываем каждый тип здоровых культур
  for (const cropType of healthyCrops) {
    const crops = decos[cropType] || [];
    if (!crops.length) continue;

    const remainingCrops: Point[] = [];
    const witheredCrops: Point[] = [];

    // Для каждой культуры решаем - засохнет ли она (30% шанс)
    for (const crop of crops) {
      if (Math.random() < 0.7) {
        witheredCrops.push(crop);
      } else {
        remainingCrops.push(crop);
      }
    }

    // Обновляем массивы декораций
    decos[cropType] = remainingCrops;
    if (witheredCrops.length > 0) {
      decos[LocationDecorations.WITHEREDCROP]!.push(...witheredCrops);
    }
  }
}

// Регистрируем новые проходы очистки
registerCleaningPass('clean-around-doors', cleanAroundDoors);
registerCleaningPass('clean-around-cars', cleanAroundCars);
registerCleaningPass('wither-crops', witherCrops);

export default {
  registerCleaningPass,
  runCleaningPasses,
  enforceOutdoorTreeSpacing,
  cleanAroundDoors,
  cleanAroundCars,
  witherCrops,
};
