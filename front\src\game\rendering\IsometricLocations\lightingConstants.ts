/**
 * Константы освещения для новой системы освещения
 * Отдельный файл для переиспользования констант в других модулях
 */

// ===== КОНСТАНТЫ РАДИАЛЬНОГО ОСВЕЩЕНИЯ =====

/**
 * Основные настройки радиального освещения
 */
export const RADIAL_LIGHTING_CONFIG = {
  ENABLE_RADIAL_LIGHTING: true,     // Включить/выключить радиальное освещение
  BASE_DARKNESS: 0.7,               // Базовый уровень затемнения (0-1)
  LIGHT_RADIUS_MULTIPLIER: 50,      // Множитель радиуса освещения в пикселях
  GRADIENT_SMOOTHNESS: 1.0,         // Плавность градиента (0-1)
  MAX_LIGHT_SOURCES: 100,           // Максимум источников света для обработки
  FALLOFF_POWER: 2,                 // Степень затухания света
} as const;

/**
 * Радиусы для разных типов источников света (в пикселях)
 */
export const LIGHT_RADIUS_CONFIG = {
  STREETLIGHT_RADIUS: 120,
  EXTERIOR_LIGHT_RADIUS: 90,
  INTERIOR_LIGHT_RADIUS: 90,
} as const;

/**
 * Настройки ореола источников света (плавность перехода света к темноте)
 */
export const LIGHT_HALO_CONFIG = {
  // Радиусы зон (0-1, где 1 = полный радиус источника)
  INNER_LIGHT_RADIUS: 0.3,         // 30% радиуса - полный свет
  MIDDLE_LIGHT_RADIUS: 0.4,        // 40% радиуса - начало плавного затухания
  OUTER_LIGHT_RADIUS: 1.0,         // 100% радиуса - полная темнота
  
  // Интенсивность света в разных зонах (0-1)
  INNER_INTENSITY: 1.0,            // Полная яркость в центре
  MIDDLE_INTENSITY: 1.0,           // Яркость в средней зоне  
  OUTER_INTENSITY: 0.0,            // Прозрачность на краю
  
  // Овальность для всех источников света
  LIGHT_ELLIPSE_X: 1.8,            // Растяжение по горизонтали
  LIGHT_ELLIPSE_Y: 1.0,            // Стандартный размер по вертикали
} as const;

// ===== КОНСТАНТЫ ОСВЕЩЕНИЯ ОТ ДЕКОРАЦИЙ =====

/**
 * Основные настройки декоративного освещения
 */
export const DECORATION_LIGHTING_CONFIG = {
  LIGHTS_ON: true,
  
  // Радиусы освещения для разных типов светильников (в тайлах)
  STREETLIGHT_RADIUS: 10,         
  EXTERIOR_LIGHT_RADIUS: 10,      
  INTERIOR_LIGHT_RADIUS: 6,       

  // Радиусы ореола (плавного влияния света за пределами основного радиуса)
  STREETLIGHT_HALO_RADIUS: 3,
  EXTERIOR_LIGHT_HALO_RADIUS: 2,
  INTERIOR_LIGHT_HALO_RADIUS: 2,
} as const;

// ===== КОНСТАНТЫ ЦВЕТОВЫХ ФИЛЬТРОВ =====

/**
 * Постоянный цветофильтр для интерьеров
 */
export const INTERIOR_COLOR_FILTER_CONFIG = {
  r: 15, g: 20, b: 30, a: 0.2  // Теплый желтоватый фильтр
} as const;

// ===== ОБЪЕДИНЕННЫЕ КОНСТАНТЫ ДЛЯ ЭКСПОРТА =====

/**
 * Полная конфигурация системы освещения
 * Объединяет все активные настройки в одном объекте для удобства использования
 */
export const LIGHTING_SYSTEM_CONFIG = {
  radial: RADIAL_LIGHTING_CONFIG,
  radius: LIGHT_RADIUS_CONFIG,
  halo: LIGHT_HALO_CONFIG,
  decoration: DECORATION_LIGHTING_CONFIG,
  filter: INTERIOR_COLOR_FILTER_CONFIG,
} as const;
