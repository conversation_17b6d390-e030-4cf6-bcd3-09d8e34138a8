import { LocationSubtype } from '../../../shared/enums';

// Веса для генерации подтипов локаций
export const SUBTYPE_WEIGHTS: [LocationSubtype, number][] = [
  [LocationSubtype.SHOP, 8],
  [LocationSubtype.TOWN, 6],
  [LocationSubtype.CAMP, 6], 
  [LocationSubtype.FARM, 6],
  [LocationSubtype.MILITARY, 5],
  [LocationSubtype.BUNKER, 8],
  [LocationSubtype.GASSTATION, 10],
  [LocationSubtype.SCHOOL, 6],
  [LocationSubtype.HOSPITAL, 6],
  [LocationSubtype.HOTEL, 6],
  [LocationSubtype.VILLAGE, 6],
  [LocationSubtype.SUBWAY, 8],
  [LocationSubtype.POLICE, 5],
  [LocationSubtype.LABORATORY, 5],
  [LocationSubtype.FACTORY, 5],
  [LocationSubtype.OTHER, 0],
];

// Обязательные подтипы, которые должны быть в каждой зоне
export const REQUIRED_SUBTYPES = [
  { subtype: LocationSubtype.SHOP, morality: [0, 6] },
  { subtype: LocationSubtype.SHOP, morality: [7, 13] },
  { subtype: LocationSubtype.SHOP, morality: [14, 20] },
  { subtype: LocationSubtype.TOWN, morality: null }
];
