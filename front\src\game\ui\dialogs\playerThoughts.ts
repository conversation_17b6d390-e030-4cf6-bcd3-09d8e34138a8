/**
 * Periodic player ambient thoughts system.
 * Spawns random dialog bubbles above player every N in-game minutes.
 */
import { gameTimeManager } from '@/game/utils/time/gameTimeManager'
import { useGameStore } from '@/game/store/gameStore'
import { showDialogBubble } from '@/game/ui/dialogs/DialogBubble'

// Pool of localized thoughts
const THOUGHTS = [
  // Сарказм / пост-ирония
  'Хм… я уже видел этот камень раньше. Это плохо.',
  'Да ладно! Кто так нагадил? Метров семь ростом был или просто очень талантливый?',
  'Похоже, тысячу лет назад тут стоял город… или мне просто хочется казаться умным археологом.',
  'Экономить силы? Отличный план. Жалко, что я — идиот, который бежит по жаре.',
  'Тишина такая, будто даже тараканы съехали. Значит сегодня без ужина.',
  'И как тут люди жили раньше? Наверное, тоже ныли, что всё говно и всё рушится.',
  'Дежа вю. Либо я здесь был, либо все руины одинаково скучные.',
  'Слишком тихо… ага, классика. Сейчас как выскочит кто-нибудь — и опять 40 баксов за штаны отдавать.',
  'Запомнить это место… да, конечно. Я же ходячий GPS без батарейки.',
  'Иногда думаю: зачем я вообще это комментирую? А потом понимаю — без меня тут было бы совсем грустно.',

  // Причитания (усталость, без сарказма)
  'Ноги гудят…',
  'Пивка бы…',
  'Спина отваливается…',
  'Каждый шаг как через болото.',
  'Что там? оазис??',
  'Хочется лечь и не вставать.',
  'Быстрее бы найти хоть какой-нибудь привал.',

  // Вздохи (короткие, простые)
  'Эх…',
  'Фух…',
  'Ох…',
  'Ха-а-а…',
  'Хм-м-м…',
  'А-а-а…',
  'О-о-ох…',
  'Э-э-э…',
  'Мда-а-а…',
  'Тьфу-у-у…',

  // Звуки (только звуки, без текста)
  '*свист*',
  '*хмык*',
  '*пф-ф-ф… мда…*',
  '*кхм-кхм-кхм*',
  '*тьфу*',
  '*хех*'
]



interface InternalState {
  lastGameMinute: number | null
  lastThoughtMinute: number | null
}

const state: InternalState = {
  lastGameMinute: null,
  lastThoughtMinute: null
}

// Интервал между мыслями в ИГРОВЫХ минутах.
// 1 реальная секунда = 1 игровая минута (судя по менеджеру: 100мс -> +6сек, значит 1с -> 1м)
// Поэтому 120-180 игровых минут ≈ 2-3 реальные минуты.
const MIN_INTERVAL = 60
const MAX_INTERVAL = 100
let nextInterval = randomInterval()

function randomInterval() {
  return Math.floor(Math.random() * (MAX_INTERVAL - MIN_INTERVAL + 1)) + MIN_INTERVAL
}

function pickThought(): string {
  return THOUGHTS[Math.floor(Math.random() * THOUGHTS.length)]
}

function maybeSpawnThought(gameMinuteAbsolute: number) {
  if (state.lastThoughtMinute == null) {
    state.lastThoughtMinute = gameMinuteAbsolute
    nextInterval = randomInterval()
    return
  }
  if (gameMinuteAbsolute - state.lastThoughtMinute >= nextInterval) {
    const { currentWorld, playerLocationPresent, currentLocation } = useGameStore.getState()
    if (!currentWorld?.player) return
    // Player iso coordinates depend on context (world or location)
    let isoX: number
    let isoY: number
    if (playerLocationPresent && currentLocation?.playerPosition) {
      isoX = currentLocation.playerPosition.x
      isoY = currentLocation.playerPosition.y
    } else {
      isoX = currentWorld.player.position.x
      isoY = currentWorld.player.position.y
    }
    showDialogBubble(isoX, isoY, pickThought(), { offsetY: -4 })
    state.lastThoughtMinute = gameMinuteAbsolute
    nextInterval = randomInterval()
  }
}

let subscribed = false
export function ensurePlayerThoughts() {
  if (subscribed) return
  subscribed = true
  gameTimeManager.addTimeUpdateCallback((t) => {
    const absoluteMinute = t.day * 24 * 60 + t.hour * 60 + t.minute
    if (absoluteMinute !== state.lastGameMinute) {
      state.lastGameMinute = absoluteMinute
      maybeSpawnThought(absoluteMinute)
    }
  })
}

// Auto-enable in browser
if (typeof window !== 'undefined') {
  // Defer to next tick to let stores initialize
  setTimeout(() => ensurePlayerThoughts(), 0)
}
