/**
 * Хук для получения размеров игрового холста (IMCOMP) по размеру окна браузера.
 * Возвращает ширину окна и высоту, умноженную на коэффициент (по умолчанию 0.83 = 83%).
 * Простой, без сторов — можно подключить в корневом компоненте и пробрасывать дальше.
 */
import { useState, useEffect } from 'react';

const DEFAULT_WIDTH = 1620;
const DEFAULT_HEIGHT = 1080;
const HEIGHT_RATIO = 0.83; 

type ImcompSize = { imcompMaxWidth: number; imcompMaxHeight: number };

export function useImcompSize(): ImcompSize {
  const isClient = typeof window !== 'undefined';

  const getSize = (): ImcompSize => ({
    imcompMaxWidth: isClient ? window.innerWidth : DEFAULT_WIDTH,
    imcompMaxHeight: isClient ? Math.round(window.innerHeight * HEIGHT_RATIO) : Math.round(DEFAULT_HEIGHT * HEIGHT_RATIO),
  });

  const [size, setSize] = useState<ImcompSize>(getSize);

  useEffect(() => {
    if (!isClient) return;
    const onResize = () => setSize(getSize());
    window.addEventListener('resize', onResize);
    // ensure initial correct value on mount
    onResize();
    return () => window.removeEventListener('resize', onResize);
  }, []);

  return size;
}
