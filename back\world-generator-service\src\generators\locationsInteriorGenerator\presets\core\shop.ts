import { PresetLocationMap } from "../presetType";

export const shopPresets: PresetLocationMap[] = [
  {
    name: 'shop_1',
    width: 23,
    height: 27,
    tokenMap: [
    [11, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 29, 999, 32, 64, 64, 64],
    [11, 999, 25, 8, 11, 60, 63, 29, 11, 59, 79, 59, 60, 60, 60, 62, 11, 999, 999, 4, 64, 64, 64],
    [11, 26, 32, 999, 11, 999, 32, 999, 11, 999, 999, 999, 32, 999, 999, 999, 11, 999, 999, 999, 64, 64, 64],
    [11, 75, 21, 999, 15, 32, 21, 999, 11, 48, 21, 999, 21, 62, 62, 62, 11, 24, 999, 5, 64, 64, 64],
    [11, 999, 999, 999, 11, 999, 999, 999, 15, 999, 32, 999, 999, 999, 32, 999, 11, 40, 999, 999, 64, 64, 64],
    [11, 11, 11, 11, 11, 29, 999, 999, 11, 999, 999, 62, 62, 62, 62, 62, 11, 40, 32, 999, 64, 64, 64],
    [11, 46, 99, 52, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 999, 999, 999, 63, 999, 999],
    [11, 999, 999, 52, 999, 999, 999, 45, 999, 99, 44, 44, 42, 44, 44, 999, 11, 999, 32, 999, 999, 999, 5],
    [11, 999, 54, 52, 54, 999, 32, 999, 999, 999, 999, 999, 999, 42, 999, 999, 15, 999, 5, 32, 5, 999, 999],
    [11, 999, 67, 52, 999, 999, 999, 66, 999, 66, 999, 999, 66, 999, 999, 32, 12, 999, 999, 5, 999, 4, 32],
    [11, 999, 999, 21, 32, 999, 32, 66, 21, 66, 999, 32, 66, 21, 999, 32, 12, 999, 9, 999, 2, 32, 999],
    [11, 17, 999, 66, 999, 999, 999, 66, 32, 66, 999, 999, 66, 999, 999, 999, 12, 32, 999, 5, 999, 999, 999],
    [11, 17, 19, 66, 999, 8, 999, 66, 999, 66, 32, 999, 66, 42, 999, 999, 12, 999, 5, 999, 3, 4, 999],
    [11, 19, 999, 66, 999, 999, 999, 66, 999, 66, 999, 999, 66, 999, 999, 999, 12, 999, 999, 4, 32, 999, 999],
    [11, 99, 999, 21, 8, 32, 999, 999, 21, 999, 32, 999, 999, 21, 8, 999, 12, 999, 5, 5, 999, 5, 999],
    [11, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 11, 29, 999, 999, 999, 2, 32],
    [11, 11, 12, 12, 12, 12, 12, 11, 15, 11, 12, 12, 12, 12, 12, 11, 11, 29, 999, 999, 5, 5, 999],
    [40, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 33, 33, 42, 999, 32, 5, 999, 9, 2],
    [42, 999, 999, 32, 999, 999, 999, 999, 999, 32, 999, 999, 32, 999, 999, 999, 999, 999, 5, 999, 32, 999, 999],
    [28, 28, 28, 28, 28, 999, 999, 32, 999, 28, 28, 999, 999, 999, 32, 999, 42, 999, 999, 999, 999, 999, 28],
    [999, 28, 2, 32, 999, 28, 32, 999, 35, 35, 35, 35, 39, 999, 999, 28, 999, 999, 999, 28, 28, 999, 999],
    [28, 28, 999, 999, 39, 999, 999, 999, 35, 35, 35, 35, 28, 28, 28, 999, 28, 29, 999, 999, 999, 28, 28],
    [36, 32, 57, 28, 999, 28, 28, 36, 32, 28, 28, 57, 32, 32, 36, 999, 999, 29, 999, 5, 2, 999, 999],
    [999, 999, 28, 28, 999, 999, 999, 999, 999, 999, 28, 999, 999, 999, 28, 999, 28, 999, 999, 9, 999, 28, 999],
    [28, 999, 999, 999, 999, 28, 28, 999, 999, 28, 28, 999, 28, 28, 28, 999, 28, 999, 28, 28, 28, 999, 5],
    [28, 28, 28, 999, 28, 28, 999, 999, 28, 28, 28, 2, 999, 5, 999, 28, 28, 28, 999, 999, 5, 28, 999],
    [999, 5, 999, 999, 999, 5, 999, 999, 999, 999, 5, 999, 5, 999, 999, 999, 5, 999, 999, 999, 999, 999, 999]
  ],
    anchor: { x: 9, y: 14 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];