import { TransferLocation, PointWithNumber } from '../../shared/types/Location';
import { LocationDecorations } from '../../shared/enums';

/**
 * Editable equivalence groups: decorations listed in the same inner array are
 * considered equal for the purposes of side detection (they will connect).
 *
 * Add new groups here when you want more types to be treated as identical.
 * Example below treats wall/window/door as the same group.
 */
export const EQUIVALENCE_GROUPS: string[][] = [
  // treat these as the same for sides: wall, window, door
  [
    LocationDecorations.WALL,
    LocationDecorations.WINDOW,
    LocationDecorations.DOOR,
    LocationDecorations.FENCE,
    LocationDecorations.PARTITION,
    LocationDecorations.GARAGEDOOR,
    LocationDecorations.JAILDOOR,
    LocationDecorations.JAILWINDOW,
    LocationDecorations.JAILBARS,
    LocationDecorations.RUINEDWALL
  ]
];

/**
 * Dependencies for decorations that should orient towards specific other decorations.
 * Key: decoration that needs to be oriented
 * Value: array of decoration types it should orient towards
 * 
 * Example: CHAIR will record sides where TABLE or TV are adjacent
 */
export const DECORATION_DEPENDENCIES: Record<string, string[]> = {
  [LocationDecorations.CHAIR]: [LocationDecorations.TABLE, LocationDecorations.TV],
  [LocationDecorations.SCHOOLCHAIR]: [LocationDecorations.TABLE],
  [LocationDecorations.CASHREGISTER]: [LocationDecorations.BARCOUNTER, LocationDecorations.WALL],
  [LocationDecorations.BADSIDETABLE]: [LocationDecorations.BAD, LocationDecorations.DOUBLEBED],
  [LocationDecorations.BIOMONITOR]: [LocationDecorations.BAD, LocationDecorations.SURGICALTABLE],
  [LocationDecorations.ARMCHAIR]: [LocationDecorations.SOFA, LocationDecorations.WALL],

};

/**
 * Programmatic helper to register equivalence groups at runtime (optional).
 */
export function addEquivalenceGroup(group: string[]) {
  if (!group || !group.length) return;
  EQUIVALENCE_GROUPS.push(group);
}

/**
 * Helper to add new decoration dependencies at runtime.
 * @param decoration - The decoration that needs to be oriented
 * @param dependencies - Array of decoration types it should orient towards
 */
export function addDecorationDependency(decoration: string, dependencies: string[]) {
  if (!decoration || !dependencies || !dependencies.length) return;
  DECORATION_DEPENDENCIES[decoration] = dependencies;
}

// Build a lookup map from decoration -> canonical group id
function buildEquivalenceLookup(): Map<string, string> {
  const map = new Map<string, string>();
  for (const group of EQUIVALENCE_GROUPS) {
    const id = group[0];
    for (const t of group) map.set(t, id);
  }
  return map;
}

function areEquivalent(a: string, b: string, lookup: Map<string, string>): boolean {
  if (a === b) return true;
  const ca = lookup.get(a) ?? a;
  const cb = lookup.get(b) ?? b;
  return ca === cb;
}

/**
 * For each decoration tile in a TransferLocation, detect which cardinal neighbours
 * contain the same decoration type (with equivalences) and write results to
 * `location.decorationSide`.
 *
 * Side numbering: 1 - north, 2 - east, 3 - south, 4 - west
 */
export function declareDecorationSides(location: TransferLocation): void {
  if (!location) return;

  const decorations = location.decorations || {};
  const eqLookup = buildEquivalenceLookup();

  // Типы, для которых стороны определяются только относительно wallLike
  const sideToWall = new Set<string>([
    LocationDecorations.POSTER,
    LocationDecorations.LOCKER,
    LocationDecorations.SAFE,
    LocationDecorations.FRIDGE,
    LocationDecorations.OVEN,
    LocationDecorations.TV,
    LocationDecorations.TOILET,
    LocationDecorations.TRASHCONTAINER,
    LocationDecorations.FURNITURE,
    LocationDecorations.CABINET,
    LocationDecorations.SINK,
    LocationDecorations.PAINTING,
    LocationDecorations.FIREPLACE,
    LocationDecorations.VENDINGMACHINE,
    LocationDecorations.TERMINAL,
    LocationDecorations.CASHREGISTER,
    LocationDecorations.BARSHALF,
    LocationDecorations.SHELF,
    LocationDecorations.FIRSTAID,
    LocationDecorations.MILITARYCONTAINER,
    LocationDecorations.WEAPONRACK,
    LocationDecorations.TOOLBOX,
    LocationDecorations.CONTAINER,
    LocationDecorations.SHOWER
  ]);


  // Какие декорации считаются стеноподобными
  const wallLike = new Set<string>([
    LocationDecorations.WALL,
    LocationDecorations.FENCE,
    LocationDecorations.WINDOW,
    LocationDecorations.PARTITION,

  ]);

  // Build map: "x,y" -> array of decoration keys present at that cell
  const posMap = new Map<string, string[]>();
  for (const decoKey of Object.keys(decorations)) {
    const points = (decorations as Record<string, [number, number]>)[decoKey] || [];
    for (const p of points) {
      const key = `${p[0]},${p[1]}`;
      const arr = posMap.get(key) || [];
      if (!arr.includes(decoKey)) arr.push(decoKey);
      posMap.set(key, arr);
    }
  }

  // Accumulator for final decoration sides entries
  const result: PointWithNumber[] = [];

  // Cardinal deltas with side numbers
  const deltas: Array<{ dx: number; dy: number; side: number }> = [
    { dx: 0, dy: -1, side: 1 }, // north
    { dx: 1, dy: 0, side: 2 }, // east
    { dx: 0, dy: 1, side: 3 }, // south
    { dx: -1, dy: 0, side: 4 } // west
  ];

  // Определение по прямой линии
  const NEAREST_ORIENT_DECORATIONS: LocationDecorations[] = [
    LocationDecorations.GASSTATIONPUMP,
    LocationDecorations.EXTERIORLIGHT,
    // add others here if they require nearest-based orientation
  ];

  function findNearestSame(pts: [number, number][], x: number, y: number): [number, number] | null {
    if (!pts || pts.length === 0) return null;
    let best: [number, number] | null = null;
    let bestDist = Infinity;
    for (const p of pts) {
      const dx = p[0] - x;
      const dy = p[1] - y;
      const dist = Math.abs(dx) + Math.abs(dy); // Manhattan
      if (dist === 0) continue; // skip self
      if (dist < bestDist) {
        bestDist = dist;
        best = [p[0], p[1]];
      }
    }
    return best;
  }

  // Определение сторон для декораций, занимающих несколько клеток
  // record sides only for the southeast-most tile of the component.
  const LARGE_MULTI_TILE: string[] = [
    LocationDecorations.CAR,
    LocationDecorations.MOUNTAINWALL,
    LocationDecorations.SEACONTAINER,
    LocationDecorations.LIQUIDTANK,
    LocationDecorations.WARMACHINE,
    LocationDecorations.FONTAIN,
    LocationDecorations.GARAGEDOOR,
    LocationDecorations.BLACKBOARD,
    LocationDecorations.BAD,
    LocationDecorations.AGROCULTURALMACHINE,
    LocationDecorations.CATTLETROUGH,
    LocationDecorations.BANNERBAR,
    LocationDecorations.BANNERHOSPITAL,
    LocationDecorations.BANNERSHOP,
    LocationDecorations.BANNERHOTEL,
    LocationDecorations.BANNERGUNS_KNIVES,
    LocationDecorations.CRIMEBOARD,
    LocationDecorations.DOUBLEBED,
    LocationDecorations.SURGICALTABLE,
    LocationDecorations.BATH,
    LocationDecorations.TRAIN,
    LocationDecorations.SOFA,
    LocationDecorations.BUNKBED,
    LocationDecorations.BILLIARDTABLE,

  ];

  // Квадратные многоблоки, которые требуют особой логики ориентации по зависимостям
  const SQUARE_MULTI_TILE_WITH_DEPENDENCIES: Record<string, string[]> = {
    [LocationDecorations.DOUBLEBED]: [LocationDecorations.BADSIDETABLE]
  };

  // For each large decoration type, find connected components among its points
  for (const largeDeco of LARGE_MULTI_TILE) {
    const pts = (decorations as Record<string, [number, number]>)[largeDeco] || [];
    if (!pts || pts.length === 0) continue;

    // Build a set of coordinates to process
    const remaining = new Set<string>(pts.map(p => `${p[0]},${p[1]}`));

    while (remaining.size > 0) {
      // take one
      const it = remaining.values();
      const first = it.next().value as string;
      const queue: string[] = [first];
      remaining.delete(first);
      const component: string[] = [first];

      // BFS to collect connected component (4-neighbour)
      while (queue.length > 0) {
        const cur = queue.shift()!;
        const [cxStr, cyStr] = cur.split(',');
        const cx = Number(cxStr);
        const cy = Number(cyStr);
        const neighbors = [`${cx + 1},${cy}`, `${cx - 1},${cy}`, `${cx},${cy + 1}`, `${cx},${cy - 1}`];
        for (const n of neighbors) {
          if (remaining.has(n)) {
            remaining.delete(n);
            queue.push(n);
            component.push(n);
          }
        }
      }

      // compute bounding box for component
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
      for (const c of component) {
        const [sxStr, syStr] = c.split(',');
        const sx = Number(sxStr);
        const sy = Number(syStr);
        if (sx < minX) minX = sx;
        if (sy < minY) minY = sy;
        if (sx > maxX) maxX = sx;
        if (sy > maxY) maxY = sy;
      }

      const width = Math.max(1, maxX - minX + 1);
      const height = Math.max(1, maxY - minY + 1);

      // southeast-most tile
      const sx = maxX;
      const sy = maxY;

      let sides: number[];

      // Проверяем, является ли это квадратным многоблоком с зависимостями
      const dependencies = SQUARE_MULTI_TILE_WITH_DEPENDENCIES[largeDeco];
      if (dependencies && width === height) {
        // Квадратный многоблок с зависимостями - определяем ориентацию по расположению зависимых объектов
        const dependencySides = new Set<number>();

        // Проверяем все клетки компонента на наличие зависимых объектов рядом
        for (const c of component) {
          const [cxStr, cyStr] = c.split(',');
          const cx = Number(cxStr);
          const cy = Number(cyStr);

          // Проверяем 4 направления от каждой клетки компонента
          const deltas = [
            { dx: 0, dy: -1, side: 1 }, // north
            { dx: 1, dy: 0, side: 2 },  // east
            { dx: 0, dy: 1, side: 3 },  // south
            { dx: -1, dy: 0, side: 4 }  // west
          ];

          for (const d of deltas) {
            const nx = cx + d.dx;
            const ny = cy + d.dy;

            // Проверяем в исходных decorations на наличие зависимых объектов
            for (const depType of dependencies) {
              const depPoints = (decorations as Record<string, [number, number][]>)[depType] || [];
              if (depPoints.some(([px, py]) => px === nx && py === ny)) {
                dependencySides.add(d.side);
              }
            }
          }
        }

        // Определяем ориентацию на основе найденных зависимостей
        const foundSides = Array.from(dependencySides);
        if (foundSides.includes(2) || foundSides.includes(4)) {
          // Если зависимые объекты с востока или запада, то ориентация север-юг
          sides = [1, 3];
        } else if (foundSides.includes(1) || foundSides.includes(3)) {
          // Если зависимые объекты с севера или юга, то ориентация восток-запад
          sides = [2, 4];
        } else {
          // Если зависимых объектов не найдено, используем обычную логику
          sides = width > height ? [2, 4] : [1, 3];
        }
      } else {
        // Обычная логика: выбор сторон на основе размеров
        sides = width > height ? [2, 4] : [1, 3];
      }

      // remove all component points from posMap so regular logic won't process them
      for (const c of component) {
        posMap.delete(c);
      }

      // add to result list (will be merged later)
      result.push([sx, sy, sides]);
    }
  }

  for (const [posKey, typesHere] of posMap.entries()) {
    const [sxStr, syStr] = posKey.split(',');
    const sx = Number(sxStr);
    const sy = Number(syStr);
    const sides = new Set<number>();

    // Если среди типов есть хотя бы один из sideToWall — применяем особую логику
    const useWallLike = typesHere.some(t => sideToWall.has(t));

    // Проверяем, есть ли среди типов декорации с зависимостями
    const hasDependencies = typesHere.some(t => DECORATION_DEPENDENCIES[t]);

    for (const d of deltas) {
      const nx = sx + d.dx;
      const ny = sy + d.dy;

      // bounds check if locationSize present
      if (location.locationSize) {
        const maxX = location.locationSize[0];
        const maxY = location.locationSize[1];
        if (nx < 0 || ny < 0 || nx >= maxX || ny >= maxY) continue;
      }

      const neighKey = `${nx},${ny}`;
      const neighTypes = posMap.get(neighKey) || [];

      if (hasDependencies) {
        // Логика для декораций с зависимостями (например, стулья)
        let dependencyMatched = false;
        for (const t of typesHere) {
          const dependencies = DECORATION_DEPENDENCIES[t];
          if (dependencies) {
            // Проверяем также в исходных decorations (до удаления больших блоков)
            let foundInOriginal = false;
            for (const depType of dependencies) {
              const depPoints = (decorations as Record<string, [number, number][]>)[depType] || [];
              if (depPoints.some(([px, py]) => px === nx && py === ny)) {
                foundInOriginal = true;
                break;
              }
            }

            // Проверяем в соседних клетках posMap И в исходных decorations
            if (neighTypes.some(nt => dependencies.includes(nt)) || foundInOriginal) {
              dependencyMatched = true;
              break;
            }
          }
        }
        if (dependencyMatched) {
          sides.add(d.side);
        }
      } else if (useWallLike) {
        // Для постеров и т.п. — только если рядом wallLike
        if (neighTypes.some(nt => wallLike.has(nt))) {
          sides.add(d.side);
        }
      } else {
        // Обычная логика эквивалентности
        let matched = false;
        for (const t of typesHere) {
          for (const nt of neighTypes) {
            if (areEquivalent(t, nt, eqLookup)) { matched = true; break; }
          }
          if (matched) { sides.add(d.side); break; }
        }
      }
    }

    // Nearest-orientation override: for specific decorations (e.g., gasStationPump)
    for (const t of typesHere) {
      if (NEAREST_ORIENT_DECORATIONS.includes(t as LocationDecorations)) {
        const allPts = (decorations as Record<string, [number, number][]>)[t] || [];
        const nearest = findNearestSame(allPts, sx, sy);
        if (nearest) {
          const dx = nearest[0] - sx;
          const dy = nearest[1] - sy;
          // if nearest is north or south (|dy| >= |dx|) -> record vertical sides [1,3]
          // else -> horizontal [2,4]
          if (Math.abs(dy) >= Math.abs(dx)) {
            sides.add(1); sides.add(3);
          } else {
            sides.add(2); sides.add(4);
          }
        }
      }
    }

    // Convert set to sorted array
    const sidesArr = Array.from(sides).sort((a, b) => a - b);
    result.push([sx, sy, sidesArr]);
  }

  location.decorationSide = result;
}

/**
 * Special function to determine stairs orientation based on nearest walls.
 * For each stairs decoration, find the nearest wall in each direction (up to 10 cells)
 * and determine orientation: vertical (1,3) or horizontal (2,4)
 */
export function declareStairsOrientation(location: TransferLocation): void {
  if (!location || !location.decorations) return;

  const stairsPositions = location.decorations[LocationDecorations.STAIRS];
  if (!stairsPositions || stairsPositions.length === 0) return;

  const decorations = location.decorations;
  const maxDistance = 10;

  // Wall-like decorations that stairs should orient towards
  const wallLike = new Set<string>([
    LocationDecorations.WALL,
    LocationDecorations.FENCE,
    LocationDecorations.PARTITION,
  ]);

  // Build position map for quick lookup
  const posMap = new Map<string, string[]>();
  for (const decoKey of Object.keys(decorations)) {
    const points = decorations[decoKey as LocationDecorations] || [];
    for (const p of points) {
      const key = `${p[0]},${p[1]}`;
      const arr = posMap.get(key) || [];
      if (!arr.includes(decoKey)) arr.push(decoKey);
      posMap.set(key, arr);
    }
  }

  // Directions: 1-north, 2-east, 3-south, 4-west
  const directions = [
    { dx: 0, dy: -1, side: 1 }, // north
    { dx: 1, dy: 0, side: 2 },  // east
    { dx: 0, dy: 1, side: 3 },  // south
    { dx: -1, dy: 0, side: 4 }  // west
  ];

  const stairsSides: PointWithNumber[] = [];

  for (const [sx, sy] of stairsPositions) {
    const distances: number[] = [Infinity, Infinity, Infinity, Infinity]; // distances to walls in each direction

    // Check each direction for nearest wall
    for (const dir of directions) {
      for (let dist = 1; dist <= maxDistance; dist++) {
        const nx = sx + dir.dx * dist;
        const ny = sy + dir.dy * dist;

        // Bounds check
        if (location.locationSize) {
          const [maxX, maxY] = location.locationSize;
          if (nx < 0 || ny < 0 || nx >= maxX || ny >= maxY) break;
        }

        const neighKey = `${nx},${ny}`;
        const neighTypes = posMap.get(neighKey) || [];

        // Check if any wall-like decoration is present
        if (neighTypes.some(type => wallLike.has(type))) {
          distances[dir.side - 1] = dist;
          break; // Found nearest wall in this direction
        }
      }
    }

    // Determine orientation based on nearest walls
    const northDist = distances[0]; // side 1
    const southDist = distances[2]; // side 3
    const eastDist = distances[1];  // side 2
    const westDist = distances[3];  // side 4

    const minVertical = Math.min(northDist, southDist);
    const minHorizontal = Math.min(eastDist, westDist);

    let orientation: number[];
    if (minVertical < minHorizontal) {
      // Vertical orientation (north-south)
      orientation = [1, 3];
    } else if (minHorizontal < minVertical) {
      // Horizontal orientation (east-west)
      orientation = [2, 4];
    } else {
      // Equal distances or no walls found - default to vertical
      orientation = [1, 3];
    }

    stairsSides.push([sx, sy, orientation]);
  }

  // Merge with existing decorationSide or create new
  if (!location.decorationSide) {
    location.decorationSide = [];
  }

  // Remove existing stairs entries and add new ones
  location.decorationSide = location.decorationSide.filter(
    ([x, y]) => !stairsPositions.some(([sx, sy]) => sx === x && sy === y)
  );

  location.decorationSide.push(...stairsSides);
}

export default declareDecorationSides;


