
  import { PresetLocationMap } from "../presetType";
  
  export const blockedRuinsPresets: PresetLocationMap[] = [
    {
      name: 'blockedRuins_1',
      width: 16,
    height: 14,
    tokenMap:  [
    [1, 999, 11, 11, 12, 11, 999, 11, 11, 15, 11, 11, 12, 11, 11, 11],
    [1, 46, 32, 119, 119, 999, 119, 32, 1, 119, 999, 119, 119, 119, 119, 11],
    [1, 1, 119, 119, 119, 119, 119, 32, 119, 119, 119, 119, 32, 119, 32, 11],
    [11, 1, 119, 32, 119, 119, 32, 119, 32, 119, 32, 119, 32, 119, 119, 11],
    [11, 75, 119, 32, 119, 32, 11, 119, 1, 80, 119, 11, 27, 119, 119, 11],
    [12, 119, 119, 119, 119, 32, 11, 16, 119, 80, 32, 11, 27, 119, 32, 999],
    [11, 119, 119, 1, 62, 119, 11, 32, 119, 119, 119, 11, 119, 119, 32, 32],
    [11, 119, 1, 32, 62, 119, 11, 119, 119, 1, 119, 12, 119, 32, 32, 119],
    [12, 32, 29, 119, 119, 999, 11, 32, 119, 119, 32, 11, 999, 119, 32, 32],
    [11, 119, 119, 119, 119, 119, 11, 11, 15, 11, 11, 11, 119, 119, 32, 999],
    [1, 1, 119, 119, 119, 119, 32, 32, 119, 119, 1, 11, 32, 119, 35, 35],
    [5, 1, 5, 119, 119, 119, 119, 119, 32, 119, 1, 11, 119, 999, 35, 35],
    [5, 999, 1, 2, 1, 32, 1, 32, 119, 32, 2, 32, 999, 999, 35, 35],
    [999, 1, 5, 5, 5, 999, 1, 1, 999, 5, 5, 999, 1, 1, 35, 35]
  ],
      anchor: { x: 0, y: 0 },
      rotations: false,
      mirror: true,
      weight: 1,
      maxInstances: 1
      
    }
  ];