.loadingScreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f0f0f 0%, #1e1e1e 50%, #0f0f0f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.loadingScreen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(183, 228, 157, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(155, 184, 145, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(102, 119, 102, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  position: relative;
  text-align: center;
  max-width: 600px;
  padding: 2rem;
}

.logo h1 {
  font-size: 3rem;
  color: #b7e49d;
  text-shadow: 0 0 20px rgba(183, 228, 157, 0.5);
  margin: 0 0 0.5rem 0;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.2rem;
  color: #9bb891;
  margin-bottom: 3rem;
}

.progressContainer {
  margin: 2rem 0;
}

.progressBar {
  width: 100%;
  height: 20px;
  background: rgba(15, 15, 15, 0.5);
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #334433;
  box-shadow: 0 0 10px rgba(148, 196, 125, 0.25);
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #494721 0%, #6b8f5c 100%);
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(180, 255, 140, 0.35);
}

.progressText {
  margin-top: 1rem;
  font-size: 1.1rem;
  color: #b7e49d;
  font-weight: bold;
}

.currentFile {
  margin: 1.5rem 0;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fileName {
  font-size: 0.9rem;
  color: #9bb891;
  opacity: 0.8;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loadingAnimation {
  margin: 2rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(51, 68, 51, 0.3);
  border-top: 3px solid #494721;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tips {
  margin-top: 3rem;
  padding: 1rem;
  background: rgba(73, 71, 33, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(51, 68, 51, 0.5);
  backdrop-filter: blur(5px);
}

.tips p {
  margin: 0;
  color: #b7e49d;
  font-size: 1rem;
  opacity: 0.9;
}

/* Анимация появления */
.loadingScreen {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Адаптивность */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .logo h1 {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
}

/* Эффект мерцания для прогресс-бара */
.progressFill {
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 10px rgba(180, 255, 140, 0.35);
  }
  100% {
    box-shadow: 0 0 20px rgba(148, 196, 125, 0.6);
  }
}
