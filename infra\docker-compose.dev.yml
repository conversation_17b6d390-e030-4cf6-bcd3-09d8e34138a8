services:
  # Databases
  postgres-auth:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-auth-dev
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_auth_data_dev:/var/lib/postgresql/data
      - ./postgres/init-auth.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - nuclearstory-network

  postgres-saves:
    image: postgres:15-alpine
    container_name: nuclearstory-postgres-saves-dev
    environment:
      POSTGRES_DB: saves_db
      POSTGRES_USER: nuclearstory
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_saves_data_dev:/var/lib/postgresql/data
      - ./postgres/init-saves.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - nuclearstory-network

  redis:
    image: redis:7-alpine
    container_name: nuclearstory-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - nuclearstory-network

  # Database Management
  adminer:
    image: adminer:4.8.1
    container_name: nuclearstory-adminer-dev
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres-auth
      - ADMINER_DESIGN=pepa-linha
    depends_on:
      - postgres-auth
      - postgres-saves
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # AI Service
  ai-service:
    build:
      context: ../ai/ai-service
      dockerfile: Dockerfile
    container_name: nuclearstory-ai-service-dev
    ports:
      - "3005:3005"
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - PORT=3005
      - CORS_ORIGIN=${FRONTEND_ORIGIN}
      - LOG_LEVEL=INFO
    volumes:
      - ../ai/ai-service:/app
      - /app/__pycache__
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # Backend Services with hot reload
  auth-service:
    build:
      context: ../back/auth-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-auth-service-dev
    ports:
      - "3001:3001"
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_HOST=postgres-auth
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=auth_db
      - JWT_SECRET=dev-super-secret-jwt-key
      - JWT_EXPIRES_IN=7d
      - CORS_ORIGIN=${FRONTEND_ORIGIN}
    volumes:
      - ../back/auth-service:/app
      - /app/node_modules
    depends_on:
      - postgres-auth
    networks:
      - nuclearstory-network
    restart: unless-stopped

  game-engine-service:
    build:
      context: ../back/game-engine-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-game-engine-service-dev
    ports:
      - "3002:3002"
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - PORT=3002
      - AUTH_SERVICE_URL=http://auth-service:3001
      - STORY_SERVICE_URL=http://world-generator-service:3003
      - SAVE_SERVICE_URL=http://save-service:3004
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CORS_ORIGIN=${FRONTEND_ORIGIN}
    volumes:
      - ../back/game-engine-service:/app
      - /app/node_modules
    depends_on:
      - auth-service
      - redis
    networks:
      - nuclearstory-network
    restart: unless-stopped

  world-generator-service:
    build:
      context: ../back/world-generator-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-world-generator-service-dev
    ports:
      - "3003:3003"
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - PORT=3003
      - AUTH_SERVICE_URL=http://auth-service:3001
      - AI_SERVICE_URL=http://ai-service:3005
      - SAVE_SERVICE_URL=http://save-service:3004
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CORS_ORIGIN=${FRONTEND_ORIGIN}
    volumes:
      - ../back/world-generator-service:/app
      - /app/node_modules
    depends_on:
      - auth-service
      - ai-service
      - redis
    networks:
      - nuclearstory-network
    restart: unless-stopped

  save-service:
    build:
      context: ../back/save-service
      dockerfile: Dockerfile.dev
    container_name: nuclearstory-save-service-dev
    ports:
      - "3004:3004"
    env_file:
      - ./.env
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_HOST=postgres-saves
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=nuclearstory
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=saves_db
      - AUTH_SERVICE_URL=http://auth-service:3001
      - CORS_ORIGIN=${FRONTEND_ORIGIN}
    volumes:
      - ../back/save-service:/app
      - /app/node_modules
    depends_on:
      - postgres-saves
      - auth-service
    networks:
      - nuclearstory-network
    restart: unless-stopped

  # Frontend with hot reload
  frontend:
    build:
      context: ../front
      dockerfile: Dockerfile.dev
      args:
        - HOST_LAN_IP=${HOST_LAN_IP}
    container_name: nuclearstory-frontend-dev
    ports:
      - "3000:3000"
    env_file:
      - ./.env
    environment:
      - VITE_API_BASE_URL=http://${HOST_LAN_IP}:3001/api
      - VITE_AUTH_SERVICE_URL=http://${HOST_LAN_IP}:3001/api/auth
      - VITE_GAME_SERVICE_URL=http://${HOST_LAN_IP}:3002/api/game
      - VITE_WORLD_GENERATOR_SERVICE_URL=http://${HOST_LAN_IP}:3003/api
      - VITE_SAVE_SERVICE_URL=http://${HOST_LAN_IP}:3004/api
      - VITE_AI_SERVICE_URL=http://${HOST_LAN_IP}:3005
      - VITE_HOST_LAN=${HOST_LAN_IP}
    volumes:
      - ../front:/app
      - /app/node_modules
    networks:
      - nuclearstory-network

volumes:
  postgres_auth_data_dev:
  postgres_saves_data_dev:
  redis_data_dev:

networks:
  nuclearstory-network:
    driver: bridge
