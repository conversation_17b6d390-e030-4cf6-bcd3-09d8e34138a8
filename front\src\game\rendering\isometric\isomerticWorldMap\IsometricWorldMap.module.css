.mapContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: black;
  overflow: hidden;
  z-index: 1;
}

.mapCanvas {
  
  position: fixed;
  top: 2px;
  cursor: default;
  background: var(--bg-primary);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  user-select: none;
  -webkit-user-drag: none;
  transform-origin: center center;
  transition: transform 0.1s ease-out;
}

.canvasDefault {
  cursor: default;
}

.instructions {
  color: var(--text-muted);
  font-size: 11px;
  max-width: 200px;
}

/* HUD wrappers moved from inline styles in IsometricWorldMap.tsx */
.hudTopLeft {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
}

.cameraButton {
  position: absolute;
  bottom: 120px;
  right: 20px;
  z-index: 100;
  cursor: pointer;
  border: 1px solid var(--primary-color);
  padding: 8px 10px 5px;
  border-radius: 8px;
}

.hudBottomRight {
  position: absolute;
  bottom: 40px;
  right: 20px;
  z-index: 100;
}
