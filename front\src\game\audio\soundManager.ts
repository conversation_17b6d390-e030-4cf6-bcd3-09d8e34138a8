import { SoundChannel, PlaySoundOptions, ActiveSound } from './channels'
import { useSoundStore } from './soundStore'

class SoundManagerClass {
  private activeSounds = new Map<string, ActiveSound>()
  private trackingNames = new Map<string, string>() // trackName -> soundId mapping
  
  private generateId(): string {
    return `sound_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Универсальная функция воспроизведения звука
   */
  playSound(
    soundPath: string, 
    channel: SoundChannel, 
    options: PlaySoundOptions = {},
    trackName?: string // опциональное имя для отслеживания
  ): string {
    const id = this.generateId()
    
    try {
      const audio = new Audio(soundPath)
      const { getEffectiveVolume } = useSoundStore.getState()
      
      // Настройка аудио
      audio.loop = options.loop || false
      audio.volume = this.calculateVolume(channel, options.volume)
      
      // Обработчики событий
      audio.addEventListener('ended', () => {
        if (options.onEnd) options.onEnd()
        this.stopSound(id)
      })
      
      audio.addEventListener('error', (e) => {
        console.warn(`❌ Ошибка загрузки звука ${soundPath}:`, e)
        this.stopSound(id)
      })
      
      // Fade-in эффект
      if (options.fadeIn && options.fadeIn > 0) {
        audio.volume = 0
        audio.play()
        this.fadeIn(audio, this.calculateVolume(channel, options.volume), options.fadeIn)
      } else {
        audio.play()
      }
      
      // Сохраняем активный звук
      const activeSound: ActiveSound = {
        id,
        audio,
        channel,
        volume: options.volume || 1,
        isLooping: audio.loop
      }
      
      this.activeSounds.set(id, activeSound)
      
      // Сохраняем связь с именем трека
      if (trackName) {
        this.trackingNames.set(trackName, id)
      }
      
      return id
      
    } catch (error) {
      console.error(`❌ Ошибка воспроизведения ${soundPath}:`, error)
      return ''
    }
  }

  /**
   * Остановка звука по ID
   */
  stopSound(soundId: string): void {
    const sound = this.activeSounds.get(soundId)
    if (sound) {
      sound.audio.pause()
      sound.audio.currentTime = 0
      this.activeSounds.delete(soundId)
      
      // Удаляем из трекинга по имени
      for (const [trackName, id] of this.trackingNames.entries()) {
        if (id === soundId) {
          this.trackingNames.delete(trackName)
          break
        }
      }
    }
  }

  /**
   * Остановка звука по имени трека
   */
  stopTrack(trackName: string): void {
    const soundId = this.trackingNames.get(trackName)
    if (soundId) {
      this.stopSound(soundId)
    }
  }

  /**
   * Остановка всех звуков определенного канала
   */
  stopChannel(channel: SoundChannel): void {
    for (const [id, sound] of this.activeSounds.entries()) {
      if (sound.channel === channel) {
        this.stopSound(id)
      }
    }
  }

  /**
   * Остановка всех звуков
   */
  stopAll(): void {
    for (const id of this.activeSounds.keys()) {
      this.stopSound(id)
    }
  }

  /**
   * Обновление громкости всех активных звуков после изменения настроек
   */
  updateAllVolumes(): void {
    for (const sound of this.activeSounds.values()) {
      sound.audio.volume = this.calculateVolume(sound.channel, sound.volume)
    }
  }

  /**
   * Проверка воспроизведения трека по имени
   */
  isTrackPlaying(trackName: string): boolean {
    const soundId = this.trackingNames.get(trackName)
    if (!soundId) return false
    
    const sound = this.activeSounds.get(soundId)
    return sound ? !sound.audio.paused : false
  }

  /**
   * Получение списка активных звуков
   */
  getActiveSounds(): ActiveSound[] {
    return Array.from(this.activeSounds.values())
  }

  private calculateVolume(channel: SoundChannel, relativeVolume: number = 1): number {
    const { getEffectiveVolume } = useSoundStore.getState()
    return getEffectiveVolume(channel) * relativeVolume
  }

  private fadeIn(audio: HTMLAudioElement, targetVolume: number, duration: number): void {
    const steps = 50
    const stepDuration = duration / steps
    const volumeStep = targetVolume / steps
    let currentStep = 0

    const fadeInterval = setInterval(() => {
      currentStep++
      audio.volume = Math.min(volumeStep * currentStep, targetVolume)
      
      if (currentStep >= steps) {
        clearInterval(fadeInterval)
      }
    }, stepDuration)
  }
}

// Экспортируем singleton
export const soundManager = new SoundManagerClass()

// Подписываемся на изменения настроек громкости
if (typeof window !== 'undefined') {
  useSoundStore.subscribe(() => {
    soundManager.updateAllVolumes()
  })
}
