import { textureLoader } from './TextureLoader';
import texturePathsData from './texturesPaths.json';

interface TexturePathsData {
  generatedAt: string;
  totalFiles: number;
  paths: string[];
}

// Глобальное состояние прогресса, чтобы разные компоненты могли безопасно читать единый процент
let globalProgress = { loaded: 0, total: 0, percentage: 0 };
type ProgressListener = (p: typeof globalProgress) => void;
const progressListeners = new Set<ProgressListener>();

export function subscribeTexturePreloadProgress(listener: ProgressListener) {
  progressListeners.add(listener);
  // немедленный пуш текущего состояния
  listener(globalProgress);
  return () => progressListeners.delete(listener);
}

function emitProgress() {
  for (const l of progressListeners) l(globalProgress);
}

/**
 * Проверяет действительно ли все текстуры находятся в кэше (а не только версия совпала)
 * Используем точную проверку по списку путей.
 */
export function isAllTexturesPreloaded(): boolean {
  const data = texturePathsData as TexturePathsData;
  const { paths } = data;
  let cached = 0;
  for (const p of paths) {
    const img = textureLoader.getTexture(p);
    if (img && img.complete) cached++;
  }
  return cached === paths.length;
}

/**
 * Загружает все текстуры из JSON файла с отчетом о прогрессе
 */
let preloadPromise: Promise<void> | null = null;
let preloadStartedAt: number | null = null;
let lastReported = 0;

export async function preloadAllTextures(
  onProgress?: (loaded: number, total: number, percentage: number, currentPath: string) => void,
  options: { force?: boolean; log?: boolean } = {}
): Promise<void> {
  if (preloadPromise && !options.force) {
    return preloadPromise; // уже идёт — возвращаем существующий промис
  }

  preloadPromise = (async () => {
  const data = texturePathsData as TexturePathsData;
  const { paths, totalFiles } = data;
  globalProgress.total = totalFiles;
  globalProgress.loaded = 0;
  globalProgress.percentage = 0;
  emitProgress();
  let loaded = 0;
  preloadStartedAt = performance.now();

  // Загружаем батчами по 20 файлов параллельно для скорости
  const batchSize = 20;
  const batches: string[][] = [];
  
  for (let i = 0; i < paths.length; i += batchSize) {
    batches.push(paths.slice(i, i + batchSize));
  }

  for (let bi = 0; bi < batches.length; bi++) {
    const batch = batches[bi];
  
    const promises = batch.map(async (path) => {
      try {
        await textureLoader.loadTexture(path);
      } catch (error) {
        // ошибка уже отработана в loader
      }
      loaded++;
      globalProgress.loaded = loaded;
      globalProgress.percentage = Math.round((loaded / totalFiles) * 100);
      if (globalProgress.percentage !== lastReported) {
        lastReported = globalProgress.percentage;
        if (onProgress) onProgress(globalProgress.loaded, totalFiles, globalProgress.percentage, path);
        emitProgress();
      }
    });
    await Promise.all(promises);
    // Пауза чтобы отрисовался прогресс
    await new Promise(resolve => setTimeout(resolve, 5));
  }

  const durationMs = preloadStartedAt ? Math.round(performance.now() - preloadStartedAt) : 0;
  if (options.log) {
    const failed = textureLoader.getFailedTextures();
    
    if (failed.length > 0) console.log('[Textures] first failed examples:', failed.slice(0, 10));
  }
  })();
  return preloadPromise;
}

/**
 * Быстрая параллельная загрузка всех текстур (без прогресса)
 */
export async function preloadAllTexturesFast(): Promise<void> {
  const data = texturePathsData as TexturePathsData;
  const { paths, totalFiles } = data;
  
  // Разбиваем на батчи по 30 файлов для оптимальной скорости
  const batchSize = 30;
  const batches: string[][] = [];
  
  for (let i = 0; i < paths.length; i += batchSize) {
    batches.push(paths.slice(i, i + batchSize));
  }
  
  // Загружаем батчи последовательно, но внутри батча - параллельно
  for (const batch of batches) {
    const promises = batch.map(path => 
      textureLoader.loadTexture(path).catch(() => {
        // Игнорируем ошибки
      })
    );
    
    await Promise.all(promises);
  }
}

/**
 * Получает общее количество текстур для загрузки
 */
export function getTotalTextureCount(): number {
  const data = texturePathsData as TexturePathsData;
  return data.totalFiles;
}

/**
 * Получает информацию о текстурах
 */
export function getTextureInfo(): TexturePathsData {
  return texturePathsData as TexturePathsData;
}
