import { PresetLocationMap } from "../presetType";

export const policePresets: PresetLocationMap[] = [
  {
    name: 'police_1',
    width:50,
    height: 40,
    tokenMap:    [
    [11, 11, 11, 12, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 12, 11, 11, 11, 11, 12, 11, 11, 11, 12, 11, 11, 11, 11, 11, 12, 11, 11, 11, 11, 12, 11, 11, 11, 12, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
    [11, 999, 20, 74, 99, 18, 11, 32, 999, 999, 999, 11, 102, 37, 999, 13, 11, 102, 99, 999, 37, 11, 102, 999, 999, 999, 11, 999, 32, 32, 999, 11, 107, 24, 999, 13, 11, 107, 999, 999, 999, 11, 22, 26, 77, 93, 93, 93, 999, 11],
    [12, 999, 20, 999, 999, 37, 11, 999, 999, 32, 999, 11, 20, 20, 21, 32, 11, 20, 20, 21, 32, 11, 20, 20, 21, 32, 11, 20, 20, 21, 13, 11, 74, 42, 21, 32, 11, 74, 999, 21, 8, 11, 75, 999, 32, 999, 32, 45, 73, 11],
    [11, 17, 21, 999, 32, 999, 15, 32, 999, 21, 999, 11, 74, 999, 32, 999, 11, 74, 999, 42, 999, 11, 74, 999, 32, 999, 11, 74, 42, 999, 999, 11, 20, 20, 999, 999, 11, 20, 20, 999, 37, 11, 74, 999, 76, 76, 999, 999, 999, 11],
    [11, 13, 32, 999, 999, 999, 11, 999, 999, 42, 999, 11, 107, 999, 999, 8, 11, 999, 32, 32, 999, 11, 8, 999, 999, 999, 11, 102, 999, 32, 999, 11, 102, 999, 32, 999, 11, 102, 32, 999, 32, 11, 77, 42, 21, 8, 42, 21, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 999, 999, 999, 999, 11, 999, 999, 999, 32, 11, 999, 999, 999, 999, 11, 999, 999, 999, 999, 11, 32, 999, 999, 999, 11, 999, 42, 999, 32, 11, 999, 999, 999, 999, 11, 32, 32, 32, 999, 999, 999, 32, 11],
    [11, 107, 20, 74, 17, 17, 11, 40, 21, 32, 999, 11, 11, 15, 11, 11, 11, 11, 15, 11, 11, 11, 11, 15, 11, 11, 11, 11, 15, 11, 11, 11, 11, 15, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11],
    [12, 999, 20, 999, 42, 32, 11, 40, 32, 999, 999, 999, 999, 999, 999, 40, 999, 999, 999, 999, 999, 99, 40, 999, 999, 75, 999, 99, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 40, 44, 999, 37, 999, 999, 999, 99, 999, 11],
    [11, 24, 32, 8, 999, 32, 15, 999, 32, 999, 77, 999, 21, 32, 999, 999, 21, 999, 32, 999, 32, 21, 999, 999, 999, 21, 999, 77, 999, 21, 999, 999, 999, 32, 21, 999, 32, 999, 32, 32, 32, 999, 32, 21, 999, 999, 999, 42, 999, 15],
    [11, 37, 999, 32, 32, 999, 11, 999, 32, 32, 999, 999, 999, 999, 999, 999, 42, 42, 999, 32, 32, 32, 999, 8, 999, 999, 999, 999, 999, 32, 999, 32, 999, 999, 32, 32, 8, 999, 999, 21, 999, 999, 999, 32, 999, 32, 42, 21, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 999, 999, 8, 32, 999, 999, 999, 37, 999, 999, 999, 999, 32, 999, 999, 999, 32, 32, 999, 32, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 999, 32, 999, 32, 32, 32, 999, 999, 999, 11],
    [11, 16, 74, 20, 17, 17, 11, 75, 999, 999, 999, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 999, 8, 999, 999, 11, 11, 11, 11, 11, 11, 11],
    [12, 99, 8, 20, 999, 32, 11, 999, 999, 999, 999, 11, 40, 44, 999, 42, 999, 32, 999, 999, 999, 44, 44, 42, 999, 75, 32, 22, 32, 999, 52, 22, 26, 11, 53, 22, 26, 999, 11, 999, 999, 999, 32, 11, 75, 60, 17, 16, 999, 11],
    [11, 24, 999, 21, 999, 32, 15, 999, 999, 37, 999, 11, 999, 999, 999, 19, 32, 999, 999, 37, 19, 999, 999, 999, 19, 19, 32, 999, 999, 999, 52, 999, 66, 11, 23, 999, 32, 999, 11, 99, 32, 21, 32, 11, 37, 19, 17, 19, 999, 12],
    [11, 37, 999, 32, 999, 999, 11, 999, 32, 999, 32, 11, 42, 19, 17, 17, 32, 42, 19, 17, 17, 999, 8, 999, 17, 17, 19, 999, 999, 999, 52, 999, 66, 11, 23, 999, 17, 32, 11, 32, 32, 32, 77, 15, 999, 32, 21, 999, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 44, 999, 32, 32, 11, 999, 37, 19, 19, 21, 999, 999, 19, 19, 21, 8, 999, 19, 999, 21, 999, 77, 999, 52, 21, 999, 11, 17, 21, 17, 999, 11, 40, 999, 32, 999, 11, 999, 999, 32, 999, 999, 11],
    [11, 16, 74, 20, 17, 17, 11, 44, 999, 32, 999, 11, 999, 32, 999, 999, 999, 8, 999, 999, 999, 32, 999, 42, 999, 999, 999, 999, 999, 999, 67, 42, 999, 11, 17, 32, 32, 8, 11, 44, 999, 32, 37, 11, 11, 11, 11, 11, 11, 11],
    [12, 24, 999, 20, 32, 32, 11, 999, 21, 999, 32, 11, 999, 19, 17, 17, 19, 999, 19, 17, 17, 999, 999, 19, 17, 17, 999, 999, 999, 999, 999, 999, 999, 15, 999, 32, 999, 66, 11, 32, 21, 999, 32, 11, 94, 37, 60, 16, 32, 11],
    [11, 13, 999, 21, 32, 999, 15, 999, 999, 8, 32, 11, 999, 999, 19, 19, 999, 999, 999, 999, 19, 999, 999, 999, 999, 19, 999, 42, 999, 999, 999, 999, 999, 11, 75, 999, 42, 66, 11, 99, 32, 999, 999, 11, 999, 999, 17, 999, 999, 12],
    [11, 37, 32, 999, 999, 999, 11, 999, 999, 999, 999, 11, 999, 999, 999, 999, 32, 32, 999, 37, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 11, 999, 66, 66, 999, 11, 42, 32, 42, 999, 15, 999, 19, 17, 19, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 40, 999, 999, 37, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 999, 999, 21, 999, 11, 60, 999, 999, 999, 999, 11],
    [11, 107, 74, 20, 37, 18, 11, 999, 999, 999, 999, 999, 42, 999, 999, 999, 999, 999, 999, 999, 999, 75, 44, 44, 999, 999, 99, 999, 999, 999, 999, 999, 999, 99, 999, 999, 32, 40, 999, 999, 999, 999, 999, 11, 11, 11, 11, 11, 11, 11],
    [11, 99, 999, 20, 999, 32, 11, 99, 32, 21, 42, 999, 32, 999, 32, 999, 999, 999, 32, 999, 999, 37, 999, 999, 999, 999, 999, 999, 999, 999, 999, 42, 999, 999, 999, 999, 32, 32, 37, 77, 999, 999, 32, 11, 75, 37, 17, 16, 999, 11],
    [12, 999, 999, 21, 32, 999, 15, 999, 999, 999, 999, 999, 999, 999, 37, 8, 21, 999, 999, 999, 21, 999, 999, 999, 77, 999, 21, 999, 32, 999, 21, 999, 42, 999, 999, 8, 999, 21, 999, 999, 32, 999, 999, 11, 999, 19, 17, 19, 999, 12],
    [11, 32, 32, 999, 32, 32, 11, 999, 999, 77, 999, 999, 999, 999, 999, 999, 999, 42, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 32, 999, 32, 999, 999, 8, 32, 15, 999, 999, 32, 32, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 999, 999, 999, 32, 11, 11, 11, 11, 11, 11, 11, 11, 11, 12, 11, 11, 11, 11, 11, 12, 11, 11, 11, 11, 12, 11, 11, 11, 11, 15, 11, 11, 32, 42, 999, 999, 11, 999, 107, 32, 32, 999, 11],
    [11, 107, 74, 20, 102, 18, 11, 40, 32, 37, 999, 11, 46, 999, 60, 60, 11, 999, 999, 999, 999, 999, 999, 32, 999, 999, 999, 32, 999, 999, 999, 999, 42, 11, 29, 60, 999, 60, 11, 32, 999, 21, 999, 11, 11, 11, 11, 11, 11, 11],
    [12, 17, 999, 20, 999, 32, 11, 999, 21, 32, 999, 11, 67, 999, 32, 999, 11, 32, 37, 32, 4, 3, 999, 5, 40, 999, 32, 5, 999, 32, 999, 999, 999, 11, 63, 21, 999, 999, 11, 32, 999, 999, 32, 11, 27, 32, 25, 32, 999, 11],
    [11, 17, 42, 21, 8, 999, 15, 42, 999, 32, 999, 15, 999, 32, 21, 999, 11, 999, 999, 49, 999, 999, 999, 5, 999, 49, 49, 49, 49, 999, 8, 42, 999, 11, 82, 82, 82, 999, 11, 32, 999, 8, 32, 11, 27, 32, 21, 999, 999, 11],
    [11, 32, 999, 32, 999, 999, 11, 75, 999, 32, 999, 11, 999, 999, 32, 999, 11, 999, 32, 49, 999, 999, 999, 2, 999, 999, 32, 5, 32, 5, 999, 999, 32, 11, 82, 82, 82, 999, 11, 32, 32, 32, 37, 15, 999, 999, 999, 32, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 32, 999, 999, 999, 11, 11, 11, 11, 11, 11, 999, 36, 49, 5, 32, 32, 999, 105, 5, 32, 32, 999, 4, 999, 2, 999, 11, 82, 82, 82, 999, 11, 42, 21, 999, 999, 11, 999, 999, 999, 999, 999, 11],
    [11, 42, 999, 11, 32, 32, 11, 99, 32, 999, 999, 11, 999, 999, 60, 60, 11, 999, 999, 49, 999, 9, 42, 105, 105, 105, 32, 999, 32, 32, 5, 999, 999, 11, 82, 82, 82, 999, 11, 999, 999, 999, 999, 11, 11, 11, 11, 11, 11, 11],
    [11, 25, 8, 15, 32, 999, 11, 999, 999, 999, 999, 11, 999, 62, 32, 37, 11, 32, 40, 999, 999, 2, 105, 105, 105, 105, 105, 8, 999, 37, 999, 3, 999, 11, 82, 82, 82, 999, 11, 32, 8, 999, 32, 11, 999, 26, 11, 26, 999, 11],
    [11, 999, 999, 11, 999, 42, 15, 999, 32, 42, 999, 11, 999, 62, 999, 59, 11, 999, 999, 999, 4, 5, 32, 105, 105, 105, 999, 32, 999, 42, 999, 999, 4, 11, 28, 21, 28, 42, 11, 999, 999, 21, 32, 11, 25, 21, 11, 21, 25, 11],
    [11, 11, 11, 11, 21, 999, 11, 32, 32, 999, 999, 15, 999, 999, 21, 59, 11, 999, 999, 49, 999, 32, 5, 999, 105, 5, 999, 4, 5, 32, 999, 999, 4, 11, 999, 28, 28, 32, 11, 999, 999, 32, 32, 11, 999, 999, 11, 32, 999, 11],
    [11, 27, 27, 11, 32, 32, 11, 999, 21, 999, 999, 11, 999, 999, 59, 32, 11, 999, 36, 49, 8, 999, 42, 32, 32, 2, 32, 32, 999, 32, 999, 32, 32, 11, 28, 8, 28, 999, 11, 37, 999, 32, 999, 11, 11, 15, 11, 15, 11, 11],
    [11, 8, 999, 15, 999, 999, 11, 40, 999, 32, 32, 11, 62, 21, 999, 999, 11, 32, 32, 49, 32, 999, 3, 999, 999, 5, 999, 32, 5, 2, 8, 999, 999, 11, 28, 28, 28, 999, 11, 999, 999, 32, 999, 15, 999, 999, 999, 999, 60, 11],
    [11, 25, 21, 11, 999, 999, 11, 32, 999, 32, 999, 11, 62, 999, 999, 32, 11, 999, 32, 49, 999, 42, 32, 32, 32, 999, 37, 5, 999, 999, 999, 32, 32, 11, 999, 28, 999, 28, 11, 999, 999, 999, 32, 11, 999, 999, 21, 999, 999, 11],
    [11, 999, 999, 11, 999, 999, 11, 999, 999, 999, 999, 11, 29, 60, 60, 999, 11, 999, 40, 32, 32, 32, 999, 2, 999, 32, 4, 32, 42, 32, 999, 999, 999, 11, 28, 28, 999, 28, 11, 999, 999, 999, 999, 11, 999, 999, 999, 999, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 999, 999, 999, 32, 999, 999, 5, 999, 999, 999, 5, 999, 999, 999, 999, 999, 11, 47, 47, 47, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11]
  ],
    anchor: { x: 25, y: 20 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];