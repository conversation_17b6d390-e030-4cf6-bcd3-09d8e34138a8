import * as React from 'react'
import { useEffect, useRef } from 'react'
import { useGameStore, DialogBubble as DialogBubbleType } from '@/game/store/gameStore'
import { isoToScreen, getTileCenterOnScreen } from '@/game/utils/coordinates/isometric'
import styles from './DialogBubble.module.css'
// Duration config constants (read-only here)


// (Опционально) экспорт для отладки в консоли
export const DialogBubbleDurationConfig = {
  BASE: 800,
  PER_CHAR: 65,
  MIN: 1500,
  MAX: 6000
}
export const DIALOG_BUBBLE_BASE_DURATION_MS = DialogBubbleDurationConfig.BASE;
export const DIALOG_BUBBLE_PER_CHAR_MS = DialogBubbleDurationConfig.PER_CHAR; 
export const DIALOG_BUBBLE_MIN_DURATION_MS = DialogBubbleDurationConfig.MIN;
export const DIALOG_BUBBLE_MAX_DURATION_MS = DialogBubbleDurationConfig.MAX;

export function calcDialogBubbleAutoDuration(text: string): number {
  const chars = text.length;
  return Math.min(
    DIALOG_BUBBLE_MAX_DURATION_MS,
    Math.max(
      DIALOG_BUBBLE_MIN_DURATION_MS,
      DIALOG_BUBBLE_BASE_DURATION_MS + chars * DIALOG_BUBBLE_PER_CHAR_MS
    )
  );
}
interface DialogBubblesLayerProps {
  width: number
  height: number
  tileWidth: number
  tileHeight: number
  cameraX: number
  cameraY: number
  zoom: number
  playerLocationPresent: boolean
}

// Время затухания (ms)
const FADE_MS = 800

function getBubbleOpacity(now: number, b: DialogBubbleType) {
  if (now >= b.expiresAt) return 0
  if (now >= b.expiresAt - FADE_MS) {
    return (b.expiresAt - now) / FADE_MS
  }
  return 1
}

export const DialogBubblesLayer: React.FC<DialogBubblesLayerProps> = ({
  width, height, tileWidth, tileHeight, cameraX, cameraY, zoom, playerLocationPresent
}) => {
  const dialogBubbles = useGameStore(s => s.dialogBubbles)
  const prune = useGameStore(s => s.pruneDialogBubbles)
  const remove = useGameStore(s => s.removeDialogBubble)
  const layerRef = useRef<HTMLDivElement>(null)

  // Тик для обновления и очистки
  useEffect(() => {
    let raf: number
    const loop = () => {
      prune()
      const now = performance.now()
      // Удаляем полностью истекшие
      dialogBubbles.filter(b => now >= b.expiresAt).forEach(b => remove(b.id))
      if (layerRef.current) {
        // Форсим ре-рендер через стейт? Здесь не нужно – zustand уже вызовет
      }
      raf = requestAnimationFrame(loop)
    }
    raf = requestAnimationFrame(loop)
    return () => cancelAnimationFrame(raf)
  }, [dialogBubbles, prune, remove])

  return (
    <div ref={layerRef} style={{ position: 'absolute', left: 0, top: 0, width, height, pointerEvents: 'none', zIndex: 950 }}>
      {dialogBubbles.map((b, index) => {
        const { x: screenXRaw, y: screenYRaw } = isoToScreen(b.isoX, b.isoY, tileWidth, tileHeight)
        const { centerX, centerY } = getTileCenterOnScreen(screenXRaw, screenYRaw, width, height, cameraX, cameraY)
        const now = performance.now()
        const opacity = getBubbleOpacity(now, b)
        const translateX = centerX
        const translateY = centerY - (tileHeight * 0.8) - (index * 24) + (b.offsetY || 0) // stack

        return (
          <div
            key={b.id}
            className={styles.bubble}
            style={{
              transform: `translate(-50%, -100%) translate(${translateX}px, ${translateY}px)`,
              opacity
            }}
          >
            {b.text}
          </div>
        )
      })}
    </div>
  )
}

// Утилита для вызова
export function showDialogBubble(isoX: number, isoY: number, text: string, opts?: { durationMs?: number; style?: 'default' | 'system' | 'warning'; offsetY?: number }) {
  const { addDialogBubble } = useGameStore.getState()
  return addDialogBubble({ isoX, isoY, text, style: opts?.style, durationMs: opts?.durationMs, offsetY: opts?.offsetY })
}
