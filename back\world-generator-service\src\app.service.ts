import { Injectable } from '@nestjs/common';
import { CreateWorldDto } from './dto/create-world.dto';
import {  generateBaseWorldAsync } from './generators/worldGenerator';
import { GenerationProgress, createCancellationToken } from './utils/asyncUtils';
import { ProgressService } from './websocket/progress.service';
import { GenerationQueue } from './queue/generationQueue';

@Injectable()
export class AppService {
  private readonly generationQueue: GenerationQueue;

  constructor(private readonly progressService: ProgressService) {
    // Создаем очередь с максимум 3 одновременными генерациями и передаем ProgressService
    this.generationQueue = new GenerationQueue(3, this.progressService);
  }

  getHello(): string {
    return 'World Generator Service работает! 🌍';
  }

  async generateWorld(createWorldDto: CreateWorldDto) {
    // Используем генератор для создания базового мира и сетки карты
    const worldShell = await generateBaseWorldAsync(createWorldDto);

    // Отправляем мир в save service для сохранения
    try {
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const saveResult = await saveResponse.json();
      return {
        success: true,
        world: saveResult.world
      };
    } catch (error) {
      throw new Error(`Failed to save generated world: ${error.message}`);
    }
  }

  async generateWorldAsync(
    createWorldDto: CreateWorldDto,
    progressCallback?: (progress: GenerationProgress) => void
  ) {
    const cancellationToken = createCancellationToken();
    
    try {
      
      // Используем асинхронный генератор для создания базового мира
      const worldShell = await generateBaseWorldAsync(
        createWorldDto,
        progressCallback,
        cancellationToken
      );


      // Отправляем мир в save service для сохранения
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const saveResult = await saveResponse.json();
      
      
      return {
        success: true,
        world: saveResult.world,
        cancellationToken // Возвращаем токен для возможной отмены
      };
    } catch (error) {
      if (error.message === 'Operation was cancelled') {
        return {
          success: false,
          cancelled: true,
          message: 'Генерация мира была отменена'
        };
      }
      
      throw new Error(`Failed to generate world: ${error.message}`);
    }
  }

  /**
   * Генерация мира с WebSocket поддержкой и очередью (НОВАЯ ПРОСТАЯ ВЕРСИЯ)
   */
  async generateWorldWithProgress(
    createWorldDto: CreateWorldDto,
    userId?: string
  ): Promise<{ sessionId: string; queuePosition?: number }> {
    // Создаем сессию для отслеживания прогресса
    const sessionId = this.progressService.createProgressSession(userId, createWorldDto.name);

    // Создаем токен отмены
    const cancellationToken = this.progressService.createCancellationToken(sessionId);

    // Добавляем задачу в очередь с простой системой прогресса
    const queueItem = this.generationQueue.addToQueue(
      sessionId,
      userId || 'anonymous',
      createWorldDto,
      null, // Убираем старый callback
      cancellationToken,
      async (item) => {
        if (item.status === 'completed' && item.world) {
          await this.saveGeneratedWorld(sessionId, item.world);
        } else if (item.status === 'failed') {
          this.progressService.notifyGenerationError(sessionId, 'Ошибка генерации мира');
        } else if (item.status === 'cancelled') {
          this.progressService.notifyGenerationComplete(sessionId, { success: false, error: 'Generation was cancelled' });
        }
      }
    );

    // Возвращаем sessionId и позицию в очереди
    return {
      sessionId,
      queuePosition: queueItem.position
    };
  }

  /**
   * Сохраняет сгенерированный мир
   */
  private async saveGeneratedWorld(sessionId: string, worldShell: any) {
    try {
      const saveStartTime = Date.now();
      console.log(`💾 [${new Date().toISOString()}] Начало сохранения мира (размер: ${JSON.stringify(worldShell).length} символов)`);

      // Этап 5: Сохранение мира (90-100%)
      this.progressService.sendProgress(sessionId, 90, 'Сохранение мира', 'Подготовка данных для сохранения');

      // Сохраняем мир
      const saveServiceUrl = process.env.SAVE_SERVICE_URL || 'http://save-service:3004';

      this.progressService.sendProgress(sessionId, 92, 'Сохранение мира', 'Отправка данных в базу данных');

      const requestStartTime = Date.now();
      const saveResponse = await fetch(`${saveServiceUrl}/api/worlds`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(worldShell),
      });

      const requestEndTime = Date.now();
      console.log(`⏱️ HTTP запрос к save-service занял: ${(requestEndTime - requestStartTime) / 1000}с`);

      this.progressService.sendProgress(sessionId, 96, 'Сохранение мира', 'Обработка ответа сервера');

      if (!saveResponse.ok) {
        const errorText = await saveResponse.text();
        throw new Error(`Save service error: ${saveResponse.statusText} - ${errorText}`);
      }

      const parseStartTime = Date.now();
      const saveResult = await saveResponse.json();
      const parseEndTime = Date.now();
      console.log(`⏱️ Парсинг ответа от save-service занял: ${(parseEndTime - parseStartTime) / 1000}с`);
      
      // Безопасно извлекаем worldId
      let worldId: string | undefined;
      if (saveResult && typeof saveResult === 'object') {
        if (saveResult.world && saveResult.world.id) {
          worldId = saveResult.world.id;
        } else if (saveResult.id) {
          worldId = saveResult.id;
        } else if (saveResult.data && saveResult.data.id) {
          worldId = saveResult.data.id;
        }
      }
      

      const saveEndTime = Date.now();
      console.log(`⏱️ Полное время сохранения мира: ${(saveEndTime - saveStartTime) / 1000}с`);

      // Финальный этап - 100%
      this.progressService.sendProgress(sessionId, 100, 'Завершено', 'Мир успешно создан и сохранен');

      // Уведомляем о завершении
      this.progressService.notifyGenerationComplete(sessionId, {
        success: true,
        worldId: worldId
      });


    } catch (error) {
      this.progressService.notifyGenerationError(sessionId, error.message);
    }
  }

  /**
   * Отмена генерации мира
   */
  cancelWorldGeneration(sessionId: string): boolean {
    const removed = this.generationQueue.removeFromQueue(sessionId);
    if (removed) {
    }
    return removed;
  }

  /**
   * Получение статистики активных сессий и очереди
   */
  getGenerationStats() {
    const queueStats = this.generationQueue.getQueueStats();
    const progressStats = this.progressService.getSessionStats();
    
    return {
      queue: queueStats,
      progress: progressStats,
      summary: {
        totalInQueue: queueStats.waiting,
        totalProcessing: queueStats.processing,
        maxConcurrent: queueStats.maxConcurrent,
        activeSessions: progressStats.activeSessions
      }
    };
  }

  /**
   * Получение информации о конкретной задаче
   */
  getTaskInfo(sessionId: string) {
    return this.generationQueue.getTaskInfo(sessionId);
  }

  /**
   * Изменение максимального количества одновременных генераций
   */
  setMaxConcurrentGenerations(max: number) {
    this.generationQueue.setMaxConcurrent(max);
    return {
      success: true,
      newMaxConcurrent: max,
      message: `Максимум одновременных генераций установлен: ${max}`
    };
  }
}