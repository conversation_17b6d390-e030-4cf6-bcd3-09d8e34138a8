import { Routes, Route, Navigate } from 'react-router-dom'
import React, { useEffect } from 'react'
import Layout from './components/Layout'
import { useImcompSize } from './game/utils/constants/hooks/useImcompSize'
import { applyImcompSize } from './game/utils/constants/rendering'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import SignupPage from './pages/SignupPage'
import MainMenuPage from './pages/MainMenuPage'
import GamePage from './game/GamePage'
import { ProtectedRoute } from './components/ProtectedRoute'
import { useAuthInit } from './hooks/useAuthInit'

function App() {
  const { isInitialized } = useAuthInit()

  // Подключаем хук размеров и апдаем модульные значения в rendering
  const { imcompMaxWidth, imcompMaxHeight } = useImcompSize()

  useEffect(() => {
    applyImcompSize(imcompMaxWidth, imcompMaxHeight)
  }, [imcompMaxWidth, imcompMaxHeight])

  // Показываем загрузку пока проверяется авторизация
  if (!isInitialized) {
    return (
      <div className="app-loading">
        <div>Загрузка...</div>
      </div>
    )
  }

  return (
    <div className="app">
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="login" element={<LoginPage />} />
          <Route path="signup" element={<SignupPage />} />
        </Route>
        
        {/* Защищенные маршруты */}
        <Route path="/menu" element={
          <ProtectedRoute>
            <MainMenuPage />
          </ProtectedRoute>
        } />

        <Route path="/game" element={
          <ProtectedRoute>
            <GamePage />
          </ProtectedRoute>
        } />

        {/* Редирект для всех остальных маршрутов */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  )
}

export default App
