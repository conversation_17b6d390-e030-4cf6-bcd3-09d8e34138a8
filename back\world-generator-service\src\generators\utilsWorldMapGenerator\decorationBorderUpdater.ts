import { WorldMapCell } from '../../shared/types/World';
import { WorldMapDecorations } from '../../shared/enums';
import {
  SimpleProgressTracker,
  CancellationToken
} from '../../utils/asyncUtils';

/**
 * Асинхронно определяет смежные границы с разной декорацией
 * Добавляет в decorationBorder номера сторон, где находятся НЕ такие же декорации
 */
export async function updateDecorationBordersAsync(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  const updateInterval = Math.max(1000, Math.floor(totalCells / 100));
  
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      
      if (!cell) {
        processedCells++;
        continue;
      }
      
      // Обрабатываем озера, реки, горы и дороги
      if (cell.decoration === WorldMapDecorations.LAKE ||
          cell.decoration === WorldMapDecorations.RIVER ||
          cell.decoration === WorldMapDecorations.MOUNTAINS ||
          cell.decoration === WorldMapDecorations.ROAD) {
        
        const decorationBorder = findAdjacentDifferentDecorations(grid, x, y, cell.decoration, worldSize);
        cell.decorationBorder = decorationBorder;
      }
      
      processedCells++;
      
      // Обновляем прогресс и освобождаем event loop
      if (processedCells % updateInterval === 0) {
        
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }
  
  
}

/**
 * Находит стороны и углы, с которых находятся НЕ такие же декорации
 * Возвращает массив номеров: 1-север, 2-восток, 3-юг, 4-запад, 5-северо-восток, 6-юго-восток, 7-юго-запад, 8-северо-запад
 */
function findAdjacentDifferentDecorations(
  grid: Record<string, WorldMapCell>,
  x: number,
  y: number,
  decoration: WorldMapDecorations,
  worldSize: number
): number[] {
  const differentBorders: number[] = [];
  
  // Направления: 1-север, 2-восток, 3-юг, 4-запад
  const directions = [
    { dir: 1, dx: 0, dy: -1 }, // север
    { dir: 2, dx: 1, dy: 0 },  // восток  
    { dir: 3, dx: 0, dy: 1 },  // юг
    { dir: 4, dx: -1, dy: 0 }  // запад
  ];
  
  // Сначала проверяем стороны и запоминаем их состояние
  const sideStates: { [key: number]: boolean } = {};
  
  for (const { dir, dx, dy } of directions) {
    const neighborX = x + dx;
    const neighborY = y + dy;
    
    let hasSameDecoration = false;
    
    // Проверяем границы карты
    if (neighborX >= 0 && neighborX < worldSize && neighborY >= 0 && neighborY < worldSize) {
      const neighborKey = `${neighborX},${neighborY}`;
      const neighborCell = grid[neighborKey];
      
      // Если у соседа такая же декорация
      if (neighborCell && neighborCell.decoration === decoration) {
        hasSameDecoration = true;
      }
    }
    
    sideStates[dir] = hasSameDecoration;
    
    // Если у соседа НЕ такая же декорация (или нет соседа) - добавляем номер стороны
    if (!hasSameDecoration) {
      differentBorders.push(dir);
    }
  }
  
  // Теперь проверяем углы
  // Углы: 5-северо-восток, 6-юго-восток, 7-юго-запад, 8-северо-запад
  const corners = [
    { dir: 5, side1: 1, side2: 2, dx: 1, dy: -1 },  // северо-восток (север + восток)
    { dir: 6, side1: 2, side2: 3, dx: 1, dy: 1 },   // юго-восток (восток + юг)
    { dir: 7, side1: 3, side2: 4, dx: -1, dy: 1 },  // юго-запад (юг + запад)
    { dir: 8, side1: 4, side2: 1, dx: -1, dy: -1 }  // северо-запад (запад + север)
  ];
  
  for (const { dir, side1, side2, dx, dy } of corners) {
    // Угол добавляется только если обе соответствующие стороны имеют такую же декорацию
    if (sideStates[side1] && sideStates[side2]) {
      const cornerX = x + dx;
      const cornerY = y + dy;
      
      let cornerHasDifferentDecoration = true;
      
      // Проверяем диагональную клетку
      if (cornerX >= 0 && cornerX < worldSize && cornerY >= 0 && cornerY < worldSize) {
        const cornerKey = `${cornerX},${cornerY}`;
        const cornerCell = grid[cornerKey];
        
        // Если диагональная клетка имеет такую же декорацию - угол не добавляем
        if (cornerCell && cornerCell.decoration === decoration) {
          cornerHasDifferentDecoration = false;
        }
      }
      
      // Добавляем угол если диагональная клетка имеет другую декорацию или отсутствует
      if (cornerHasDifferentDecoration) {
        differentBorders.push(dir);
      }
    }
  }
  
  return differentBorders;
}