
import { WorldMapCell } from '../../shared/types/World';
import { LocationConfig } from './constants/locationConfig';
import { LocationType, LocationSubtype, LocationDecorations } from '../../shared/enums';
import { getPresetsForLocation } from './presets';
import { PresetLocationMap } from './presets/presetType';
import tokenLegend from './presets/tokenLegend';
import { applyBuildingsPresets } from './buildingsPresetLogic';

type LegendMap = Record<string, string | number>;

// Применяет пресеты декораций на локации
export async function placePresetsForLocation(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	const location = cell.location!;
	if (!location) return;

	// Проверяем тип локации согласно требованиям
	if ((config.type === LocationType.INDOOR || config.type === LocationType.UNDERGROUND)) {
		await applyIndoorUndergroundPreset(location, config, rng, legend);
	} else if (config.type === LocationType.OUTDOOR || config.type === LocationType.BEACH) {
		await applyOutdoorPreset(location, config, rng, legend);
	}
}

// Применяет пресет для OUTDOOR локаций (центрует пресет, не меняет location.locationSize)
async function applyOutdoorPreset(
	location: any,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	// Используем subtype из location
	const subtype = location.subtype || location.subType;

	// Список одно-структурных подтипов, которые берут 1 core-пресет
	const singleStructureSubtypes = [
		LocationSubtype.POLICE,
		LocationSubtype.HOTEL,
		LocationSubtype.HOSPITAL,
		LocationSubtype.SCHOOL,
		LocationSubtype.GASSTATION,
		LocationSubtype.SHOP,
		LocationSubtype.LABORATORY,
		LocationSubtype.MILITARY,
	];

	// Список комплексных подтипов — используем новую логику размещения
	const complexSubtypes = [
		LocationSubtype.TOWN,
		LocationSubtype.VILLAGE,
		LocationSubtype.CAMP,
		LocationSubtype.FARM
	];

	if (complexSubtypes.includes(subtype)) {
		// Используем новую логику размещения зданий для комплексных локаций
		await applyBuildingsPresets(location, config, subtype, rng, legend);
		return;
	}

	if (!singleStructureSubtypes.includes(subtype)) {
		return; // пока не поддерживаем прочие outdoor подтипы
	}

	// Получаем пресеты core для outdoor subtypes
	const presets = getPresetsForLocation(LocationType.OUTDOOR, subtype);
	if (!presets || presets.length === 0) return;

	const selectedPreset = selectRandomPreset(presets, rng);

	// Размер локации уже подготовлен к этому этапу
	const [locW, locH] = location.locationSize;

	// Вычисляем смещение для центрирования пресета
	let offsetX = Math.floor((locW - selectedPreset.width) / 2);
	let offsetY = Math.floor((locH - selectedPreset.height) / 2);

	// Если пресет больше локации, начинаем с 0 и будем обрезать при наложении
	if (offsetX < 0) offsetX = 0;
	if (offsetY < 0) offsetY = 0;

	// Очищаем область (только decorations) перед применением пресета
	await clearArea(location, offsetX, offsetY, selectedPreset.width, selectedPreset.height);

	// Применяем tokenMap пресета в координатах локации (с учётом зеркалирования)
	if (!location.decorations) location.decorations = {};

	const tokenToDecoration: Record<number, string> = {};
	for (const [decorationName, token] of Object.entries(tokenLegend)) {
		tokenToDecoration[token as number] = decorationName;
	}

	// note: spawn and goBack zone handling removed for outdoor presets

	const readTokenNum = (x: number, y: number) => {
		const srcX = selectedPreset.mirror ? (selectedPreset.width - 1 - x) : x;
		const token = selectedPreset.tokenMap[y] && selectedPreset.tokenMap[y][srcX];
		return typeof token === 'number' ? token : token ? parseInt(token.toString()) : NaN;
	};

	for (let y = 0; y < selectedPreset.height; y++) {
		for (let x = 0; x < selectedPreset.width; x++) {
			const tokenNum = readTokenNum(x, y);
			if (isNaN(tokenNum)) continue;

			const targetX = offsetX + (selectedPreset.mirror ? (selectedPreset.width - 1 - x) : x);
			const targetY = offsetY + y;

			// Пропускаем клетки вне локации
			if (targetX < 0 || targetX >= locW || targetY < 0 || targetY >= locH) continue;

			// skip special spawn/goBack handling for outdoor presets

			const decorationName = tokenToDecoration[tokenNum];
			if (decorationName && decorationName !== 'none') {
				if (!location.decorations[decorationName]) location.decorations[decorationName] = [];
				location.decorations[decorationName].push([targetX, targetY]);
			}
		}
	}



	}

/**
 * Очищает только decorations внутри указанной области (включая 1 клетку запаса вокруг)
 */
async function clearArea(
	location: any,
	startX: number,
	startY: number,
	width: number,
	height: number
): Promise<void> {
	if (!location.decorations) return;

	for (const decorationType in location.decorations) {
		const decorations = location.decorations[decorationType as any];
		if (!decorations) continue;

		for (let i = decorations.length - 1; i >= 0; i--) {
			const [x, y] = decorations[i];
			if (x >= startX - 1 && x < startX + width + 1 && y >= startY - 1 && y < startY + height + 1) {
				decorations.splice(i, 1);
			}
		}
	}
}

// Применяет пресет для INDOOR + UNDERGROUND локаций
async function applyIndoorUndergroundPreset(
	location: any,
	config: LocationConfig,
	rng: () => number,
	legend: LegendMap
): Promise<void> {
	// Очищаем существующие декорации, спавн и go back зоны
	clearLocationDecorations(location);

	// Получаем пресеты для данного типа локации
	const presets = getPresetsForLocation(config.type, location.subtype);

	if (!presets || presets.length === 0) {
		return;
	}

	// Выбираем случайный пресет
	const selectedPreset = selectRandomPreset(presets, rng);

	// Применяем пресет
	await applyPresetToLocation(location, selectedPreset, legend);
}

// Очищает декорации и зоны в локации
function clearLocationDecorations(location: any): void {
	// Удаляем все декорации
	location.decorations = {};

	// Очищаем спавн зоны
	location.spawnPosition = [0, 0];

	// Очищаем go back зоны
	location.goBackPosition = [];

	// Меняем тип пресета (не понятно что это, оставляю как есть)
}

// Выбирает случайный пресет из массива
function selectRandomPreset(presets: PresetLocationMap[], rng: () => number): PresetLocationMap {
	const randomIndex = Math.floor(rng() * presets.length);
	const selectedPreset = presets[randomIndex];
	
	// С шансом 50% применяем изометрическое зеркалирование (меняем X и Y местами)
	if (rng() < 0.5) {
		return applyIsometricMirror(selectedPreset);
	}
	
	return selectedPreset;
}

// Применяет изометрическое зеркалирование - меняет X и Y координаты местами
function applyIsometricMirror(preset: PresetLocationMap): PresetLocationMap {
	const newTokenMap: (string | number)[][] = [];
	
	// Транспонируем матрицу (меняем строки и столбцы местами)
	for (let x = 0; x < preset.width; x++) {
		newTokenMap[x] = [];
		for (let y = 0; y < preset.height; y++) {
			newTokenMap[x][y] = preset.tokenMap[y][x];
		}
	}
	
	return {
		...preset,
		width: preset.height,  // меняем размеры местами
		height: preset.width,
		tokenMap: newTokenMap,
		name: `${preset.name}_isometric_mirror`
	};
}

// Применяет выбранный пресет к локации
async function applyPresetToLocation(
	location: any,
	preset: PresetLocationMap,
	legend: LegendMap
): Promise<void> {
	// Очищаем локацию
	location.decorations = {};
	location.goBackPosition = [];
	location.spawnPosition = [0, 0];

	// Устанавливаем размер грида из пресета
	location.locationSize = [preset.width, preset.height];

	// Применяем декорации из tokenMap используя готовый tokenLegend
	await applyTokenMapDecorations(location, preset);

	// Находим двери на краях и создаем зоны
	// Only create doors/zones for indoor/underground presets. Guard against accidental calls
	// when this function is used with OUTDOOR locations.
	if (location.type !== LocationType.OUTDOOR && location.type !== LocationType.BEACH) {
		await createDoorsAndZones(location, preset);
	}

}

// Применяет декорации из tokenMap используя готовый tokenLegend
async function applyTokenMapDecorations(location: any, preset: PresetLocationMap): Promise<void> {
	const decorations: any = {};

	// Создаем обратный маппинг из tokenLegend
	const tokenToDecoration: Record<number, string> = {};
	for (const [decorationName, token] of Object.entries(tokenLegend)) {
		tokenToDecoration[token as number] = decorationName;
	}

	// Helper: get numeric token from preset at logical coordinates (x,y), taking mirror into account
	const getTokenNum = (x: number, y: number) => {
		const srcX = preset.mirror ? (preset.width - 1 - x) : x;
		const token = preset.tokenMap[y][srcX];
		return typeof token === 'number' ? token : parseInt(token.toString());
	};

	// Проходим по tokenMap и собираем декорации (с учётом зеркальности по X если preset.mirror === true)
	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const tokenNum = getTokenNum(x, y);

			// Пропускаем пустые токены
			if (tokenNum === 999 || tokenNum === 100) continue;

			const decorationName = tokenToDecoration[tokenNum];
			if (decorationName && decorationName !== 'none') {
				if (!decorations[decorationName]) {
					decorations[decorationName] = [];
				}

				// target X (может быть зеркальным)
				const targetX = preset.mirror ? (preset.width - 1 - x) : x;
				decorations[decorationName].push([targetX, y]);
			}
		}
	}

	location.decorations = decorations;
}

// Создает зоны для дверей
async function createDoorsAndZones(location: any, preset: PresetLocationMap): Promise<void> {
	if (location.locationType == LocationType.OUTDOOR) return;
	const doorToken = tokenLegend['door']; // 15
	const doorPositions: [number, number][] = [];

	// Находим двери на краях карты
	// Helper: read token considering mirror
	const readTokenNum = (x: number, y: number) => {
		const srcX = preset.mirror ? (preset.width - 1 - x) : x;
		const token = preset.tokenMap[y][srcX];
		return typeof token === 'number' ? token : parseInt(token.toString());
	};

	for (let y = 0; y < preset.height; y++) {
		for (let x = 0; x < preset.width; x++) {
			const tokenNum = readTokenNum(x, y);

			if (tokenNum === doorToken) {
				// Проверяем, на краю ли дверь
				const isOnEdge = x === 0 || x === preset.width - 1 || y === 0 || y === preset.height - 1;
				if (isOnEdge) {
					// Сохраняем реальные (целевые) координаты с учётом зеркалирования
					const targetX = preset.mirror ? (preset.width - 1 - x) : x;
					doorPositions.push([targetX, y]);
				}
			}
		}
	}

	// Создаем go back зоны в радиусе 2 от дверей
	const goBackZones: [number, number][] = [];
	for (const [doorX, doorY] of doorPositions) {
		for (let dy = -2; dy <= 2; dy++) {
			for (let dx = -2; dx <= 2; dx++) {
				const x = doorX + dx;
				const y = doorY + dy;
				if (x >= 0 && x < preset.width && y >= 0 && y < preset.height) {
					goBackZones.push([x, y]);
				}
			}
		}
	}

	// Спавн позиция - ищем подходящее место в радиусе 1-3 от двери
	let spawnPosition: [number, number];
	if (doorPositions.length > 0) {
		const [doorX, doorY] = doorPositions[0];

		// Ищем свободное место в радиусе 1-3 от двери (doorX/doorY уже target coords)
		spawnPosition = await findValidSpawnPosition(doorX, doorY, preset);
	} else {
		// Если дверей нет, спавним в центре
		spawnPosition = [Math.floor(preset.width / 2), Math.floor(preset.height / 2)];
	}

	// Устанавливаем зоны
	location.goBackPosition = goBackZones;
	location.spawnPosition = spawnPosition;

}

// Находит подходящую спавн позицию в радиусе 1-3 от двери
async function findValidSpawnPosition(doorX: number, doorY: number, preset: PresetLocationMap): Promise<[number, number]> {
	// Helper: read token considering mirror (preset may be mirrored horizontally)
	const readTokenNum = (x: number, y: number) => {
		const srcX = preset.mirror ? (preset.width - 1 - x) : x;
		const token = preset.tokenMap[y][srcX];
		return typeof token === 'number' ? token : parseInt(token.toString());
	};

	// Проверяем позиции в радиусе 1-3 от двери
	for (let distance = 3; distance <= 4; distance++) {
		for (let dy = -distance; dy <= distance; dy++) {
			for (let dx = -distance; dx <= distance; dx++) {
				// Проверяем что это именно нужное расстояние
				if (Math.abs(dx) + Math.abs(dy) !== distance) continue;

				const x = doorX + dx;
				const y = doorY + dy;

				// Проверяем границы
				if (x >= 0 && x < preset.width && y >= 0 && y < preset.height) {
					// Проверяем что это не стена или препятствие
					const tokenNum = readTokenNum(x, y);

					// Если это пустое место (999) или пол, то подходит
					if (tokenNum === 999 || tokenNum === 100) {
						return [x, y];
					}
				}
			}
		}
	}

	// Если не нашли, возвращаем позицию рядом с дверью
	return [doorX, doorY];
}