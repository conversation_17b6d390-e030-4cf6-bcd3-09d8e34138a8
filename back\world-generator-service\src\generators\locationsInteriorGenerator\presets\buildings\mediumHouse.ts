import { PresetLocationMap } from "../presetType";

export const mediumHousePresets: PresetLocationMap[] = [
  {
    name: 'mediumHouse_1',
    width: 16,
  height: 10,
  tokenMap: [
    [11, 11, 11, 12, 11, 12, 11, 11, 12, 11, 11, 11, 11, 11, 11, 11],
    [11, 18, 23, 17, 17, 40, 11, 110, 110, 102, 999, 11, 25, 27, 27, 11],
    [11, 22, 999, 999, 32, 999, 11, 110, 110, 65, 999, 11, 26, 8, 32, 11],
    [11, 24, 32, 107, 21, 999, 15, 999, 999, 21, 999, 15, 999, 21, 999, 11],
    [11, 999, 999, 14, 32, 999, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
    [11, 41, 999, 14, 999, 999, 11, 79, 72, 72, 999, 999, 75, 60, 999, 11],
    [11, 999, 999, 21, 37, 999, 15, 999, 32, 999, 17, 17, 999, 72, 999, 12],
    [11, 99, 999, 32, 999, 999, 11, 62, 999, 21, 19, 19, 21, 72, 999, 11],
    [11, 999, 999, 999, 999, 999, 11, 62, 999, 999, 999, 999, 32, 999, 999, 12],
    [11, 12, 11, 15, 11, 12, 11, 11, 12, 11, 11, 11, 12, 11, 11, 11]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];