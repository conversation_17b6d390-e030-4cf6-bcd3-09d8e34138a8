import { PresetLocationMap } from "../presetType";

export const barakPresets: PresetLocationMap[] = [
  {
    name: 'barak_1',
    width: 20,
  height: 8,
  tokenMap: [
    [11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11],
    [11, 80, 78, 80, 78, 80, 999, 22, 22, 23, 26, 26, 40, 11, 25, 73, 25, 73, 25, 11],
    [11, 80, 999, 80, 999, 80, 999, 999, 999, 999, 999, 999, 999, 15, 999, 999, 999, 999, 999, 11],
    [11, 999, 999, 999, 999, 999, 999, 999, 999, 19, 17, 19, 999, 11, 11, 11, 11, 11, 11, 11],
    [11, 104, 999, 104, 999, 104, 999, 104, 999, 999, 17, 19, 999, 11, 27, 73, 27, 73, 27, 11],
    [11, 80, 999, 80, 999, 80, 999, 80, 999, 999, 17, 19, 999, 11, 27, 73, 27, 73, 27, 11],
    [11, 80, 999, 80, 999, 80, 999, 80, 999, 999, 999, 999, 999, 15, 999, 999, 999, 999, 999, 11],
    [11, 11, 11, 11, 11, 11, 11, 11, 11, 15, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];