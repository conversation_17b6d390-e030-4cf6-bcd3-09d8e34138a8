import { PresetLocationMap } from "../presetType";

export const parkPresets: PresetLocationMap[] = [
  {
    name: 'park_1',
    width: 16,
  height: 10,
  tokenMap: [
    [999, 2, 999, 999, 999, 5, 5, 5, 999, 33],
    [999, 999, 999, 32, 40, 49, 49, 49, 49, 999],
    [5, 49, 28, 999, 28, 28, 999, 28, 999, 2],
    [5, 49, 28, 999, 28, 28, 28, 28, 28, 999],
    [999, 49, 32, 28, 36, 5, 33, 32, 999, 999],
    [999, 40, 32, 999, 2, 999, 999, 32, 999, 32],
    [5, 999, 28, 28, 999, 5, 105, 105, 105, 999],
    [999, 49, 28, 28, 5, 5, 105, 105, 105, 32],
    [999, 49, 999, 28, 3, 999, 105, 105, 105, 32],
    [2, 999, 32, 999, 36, 2, 999, 32, 999, 999]
  ],
    anchor: { x: 0, y: 0 },
    rotations: false,
    mirror: true,
    weight: 1,
    maxInstances: 1
    
  }
];