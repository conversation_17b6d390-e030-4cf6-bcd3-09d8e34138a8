import texturePathsData from './texturesPaths.json';

interface TexturePathsData { generatedAt: string; totalFiles: number; paths: string[] }

// Map folder (ending with /) -> sorted numeric variations (numbers without extension)
const folderVariations: Map<string, number[]> = new Map();

const data = texturePathsData as TexturePathsData;
for (const fullPath of data.paths) {
  if (!fullPath.endsWith('.png')) continue;
  const lastSlash = fullPath.lastIndexOf('/') + 1;
  if (lastSlash <= 0) continue;
  const folder = fullPath.substring(0, lastSlash); // keep trailing slash
  const fileName = fullPath.substring(lastSlash, fullPath.length - 4); // without .png
  if (!/^\d+$/.test(fileName)) continue; // numeric only
  const num = parseInt(fileName, 10);
  if (!folderVariations.has(folder)) folderVariations.set(folder, []);
  folderVariations.get(folder)!.push(num);
}

// Sort & unique
for (const [folder, list] of folderVariations) {
  const uniq = Array.from(new Set(list)).sort((a,b)=>a-b);
  folderVariations.set(folder, uniq);
}

export function getVariationsForFolder(folder: string): number[] | undefined {
  return folderVariations.get(folder);
}

export function getRandomDeterministicVariation(folder: string, x: number, y: number, seedKey: string): number {
  const list = folderVariations.get(folder);
  if (!list || list.length === 0) return 1;
  // Simple deterministic hash
  const seedStr = folder + '|' + seedKey + '|' + x + '|' + y;
  let h = 0;
  for (let i=0;i<seedStr.length;i++) {
    h = (h * 131 + seedStr.charCodeAt(i)) >>> 0;
  }
  const idx = h % list.length;
  return list[idx];
}

export function getVersionTag(): string { return (texturePathsData as TexturePathsData).generatedAt; }
