import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { DECORATION_CONFIG } from '../worldGeneratorConstants';
import { generateForestAsync } from './forestGenerator';
import { generateSwampAsync } from './swampGenerator';
import { generateRuinsAsync } from './ruinsGenerator';
import { generateRubbleAsync } from './rubbleGenerator';
import { generatePatternDecorationsAsync } from './generatePatternDecorationsAsync';
import { generateStableDecorationsAsync } from './stableDecorationGenerator';
import { WorldMapDecorations } from 'src/shared';
import {
  SimpleProgressTracker,
  CancellationToken,
  processBatch
} from '../../utils/asyncUtils';

// Асинхронная генерация декораций - НОВАЯ СТАБИЛЬНАЯ СИСТЕМА
export async function generateDecorationsAsync(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  
  // Простая генерация декораций без сложных вложенных трекеров
  await generateStableDecorationsAsync(
    grid,
    worldSize,
    rng,
    cancellationToken
  );

  // Этап 2: Шаблонные декорации (горы, реки, озера) - только если есть маркеры
  // Находим террариан маркеры для шаблонной генерации
  const markers: Position[] = [];
  let processedCells = 0;
  const totalCells = worldSize * worldSize;

  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }

      const cellKey = `${x},${y}`;
      if (grid[cellKey].terrarianMarker === 1) {
        markers.push({ x, y });
      }

      processedCells++;
      // Освобождаем event loop каждые 2000 ячеек
      if (processedCells % 2000 === 0) {
        // Не обновляем прогресс здесь, так как это быстрая операция
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }


  if (markers.length > 0) {
    // Этап 3: Шаблонные декорации (горы, реки, озера)
    await generatePatternDecorationsAsync(
      grid,
      worldSize,
      rng,
      cancellationToken
    );
  }
}

// СТАРАЯ СИСТЕМА (оставляем для совместимости, но переименовываем)
export async function generateDecorationsLegacyAsync(
  grid: Record<string, WorldMapCell>, 
  worldSize: number, 
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  // Находим все маркеры асинхронно
  const markers: Position[] = [];
  let processedCells = 0;
  const totalCells = worldSize * worldSize;
  
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }
      
      const cellKey = `${x},${y}`;
      if (grid[cellKey].terrarianMarker === 1) {
        markers.push({ x, y });
      }
      
      processedCells++;
      // Освобождаем event loop каждые 1000 ячеек
      if (processedCells % 1000 === 0) {
        await new Promise(resolve => setImmediate(resolve));
      }
    }
  }


  if (markers.length === 0) {
    
    return;
  }

  // Этап 1: Алгоритмические декорации
  const algorithmicStages = [
    { name: 'Лес', generator: generateForestAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.FOREST },
    { name: 'Болота', generator: generateSwampAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.SWAMP },
    { name: 'Мусор', generator: generateRubbleAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.RUBBLE },
    { name: 'Руины', generator: generateRuinsAsync, chance: DECORATION_CONFIG.DECORATION_CHANCES.RUINS },
  ];

  // Проходим по алгоритмическим декорациям
  for (let stageIndex = 0; stageIndex < algorithmicStages.length; stageIndex++) {
    const stage = algorithmicStages[stageIndex];
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    const startIndex = Math.floor(rng() * markers.length);
    const reorderedMarkers = markers.slice(startIndex).concat(markers.slice(0, startIndex));

    let processedMarkers = 0;
    for (const marker of reorderedMarkers) {
      if (cancellationToken?.isCancelled) {
        throw new Error('Operation was cancelled');
      }

      // Проверяем шанс генерации
      if (rng() * 100 < stage.chance) {
        await stage.generator(grid, marker, worldSize, rng, cancellationToken);
      }
      
      processedMarkers++;
      // Освобождаем event loop каждые 10 маркеров
      if (processedMarkers % 10 === 0) {
        
        await new Promise(resolve => setImmediate(resolve));
      }
    }
    
    await new Promise(resolve => setImmediate(resolve));
  }

  // Этап 2: Шаблонные декорации (горы, реки, озера)

 
  
  await generatePatternDecorationsAsync(
    grid, 
    worldSize, 
    rng, 
    cancellationToken
  );

}

// Алиас для обратной совместимости - теперь использует новую стабильную систему
export const generateDecorations = generateDecorationsAsync;

// Получение названия декорации для отображения прогресса
function getDecorationName(decoration: WorldMapDecorations): string {
  const names = {
    [WorldMapDecorations.FOREST]: 'Лес',
    [WorldMapDecorations.MOUNTAINS]: 'Горы',
    [WorldMapDecorations.LAKE]: 'Озеро',
    [WorldMapDecorations.RIVER]: 'Река',
    [WorldMapDecorations.SWAMP]: 'Болото',
    [WorldMapDecorations.RUINS]: 'Руины',
    [WorldMapDecorations.RUBBLE]: 'Обломки',
    [WorldMapDecorations.NONE]: 'Нет',
    [WorldMapDecorations.BUSHES]: 'Кусты',
    [WorldMapDecorations.CITY]: 'Город',
    [WorldMapDecorations.ROAD]: 'Дорога'
  };
  
  return names[decoration] || 'Неизвестная декорация';
}