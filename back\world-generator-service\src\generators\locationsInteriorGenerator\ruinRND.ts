import { PresetLocationMap } from './presets/presetType';
import { MaterialTexture, TerrainType } from '../../shared/enums';

// Token constants (pulled from tokenLegend for readability)
const NONE = 999;
const WALL = 11;
const WINDOW = 12;
const DOOR = 15;

// Debris / ruin interior decoration candidate tokens with convenient weights
const DEBRIS_TOKENS = [
	// Very common - basic rubble/clutter (40% of debris)
	{ token: 32, weight: 10 },  // litter
	{ token: 1, weight: 18 },   // rocks
	{ token: 119, weight: 30 }, // ruin fragments
	
	// Common - furniture/containers (35% of debris)
	{ token: 34, weight: 1 },   // box
	{ token: 59, weight: 2 },   // pallet
	{ token: 29, weight: 1 },   // barrel
	{ token: 40, weight: 1 },   // trashBin
	{ token: 63, weight: 1 },   // toolbox
  { token: 107, weight: 1 },   // Armchair
  { token: 65, weight: 1 },   // carpet
  { token: 104, weight: 1 },   // container
  { token: 22, weight: 1 },   // fridge
	{ token: 33, weight: 1 },   // tire
	{ token: 72, weight: 1 },   // bookShelf
	// Medium - larger items (20% of debris)
	{ token: 60, weight: 1 },   // locker
	{ token: 24, weight: 1 },   // tv
	{ token: 2, weight: 2 },    // tree (overgrown)
	
	// Rare - special items (5% of debris)
	{ token: 42, weight: 2 },   // skeleton
	{ token: 46, weight: 1 }    // safe
];

// Build weighted array once for performance
let WEIGHTED_DEBRIS: number[] = [];
function initWeightedDebris() {
	if (WEIGHTED_DEBRIS.length > 0) return;
	for (const item of DEBRIS_TOKENS) {
		for (let i = 0; i < item.weight; i++) {
			WEIGHTED_DEBRIS.push(item.token);
		}
	}
}

type RNG = () => number;

function defaultRng(): number { return Math.random(); }

interface GenerateOptions {
	rng?: RNG;
	// probability tweaks (optional future extension)
	wallBreakChance?: number;    // chance to break a wall segment (becomes RUIN or NONE)
	windowChance?: number;       // chance to turn eligible wall segment into window
	debrisDensity?: number;      // base probability per interior tile to place debris
	holeClusters?: number;       // number of interior hole clusters
	holeClusterRadius?: [number, number]; // min/max radius for hole clusters
	doorOnAnySide?: boolean;     // allow choosing any side (default true)
	ruinReductionFactor?: number; // fraction of remaining RUIN to convert to NONE (simulate collapse)
}

/**
 * Generates a procedural ruin preset of exact width/height.
 * 1) Initialize all cells as NONE (999)
 * 2) Build perimeter walls with doors/windows 
 * 3) Add random interior wall segments (serpentine)
 * 4) Fill remaining NONE cells with debris according to weights
 */
export function generateRuinPreset(width: number, height: number, options: GenerateOptions = {}): PresetLocationMap {
	initWeightedDebris();
	const rng = options.rng || defaultRng;

	// 1) Initialize all cells as NONE (999) - standard for decorations
	const map: number[][] = new Array(height).fill(null).map(() => new Array(width).fill(NONE));

	// 2) Build perimeter walls with 80% chance per segment (simulate holes in walls)
	if (width >= 3 && height >= 3) {
		// Top and bottom walls
		for (let x = 0; x < width; x++) {
			if (rng() < 0.8) map[0][x] = WALL;              // top wall with holes
			if (rng() < 0.8) map[height - 1][x] = WALL;     // bottom wall with holes
		}
		// Left and right walls  
		for (let y = 0; y < height; y++) {
			if (rng() < 0.8) map[y][0] = WALL;              // left wall with holes
			if (rng() < 0.8) map[y][width - 1] = WALL;      // right wall with holes
		}

		// Add doors (max 2) - avoid corners and adjacent doors
		let doorCount = 0;
		const maxDoors = 2;
		while (doorCount < maxDoors && doorCount < 10) { // safety limit
			const side = Math.floor(rng() * 4); // 0=top, 1=right, 2=bottom, 3=left
			let x, y, canPlace = true;
			
			if (side === 0) { // top
				x = 1 + Math.floor(rng() * (width - 2));
				y = 0;
			} else if (side === 1) { // right
				x = width - 1;
				y = 1 + Math.floor(rng() * (height - 2));
			} else if (side === 2) { // bottom
				x = 1 + Math.floor(rng() * (width - 2));
				y = height - 1;
			} else { // left
				x = 0;
				y = 1 + Math.floor(rng() * (height - 2));
			}

			// Check if door can be placed (not adjacent to another door)
			for (let dy = -1; dy <= 1; dy++) {
				for (let dx = -1; dx <= 1; dx++) {
					const nx = x + dx, ny = y + dy;
					if (nx >= 0 && ny >= 0 && nx < width && ny < height) {
						if (map[ny][nx] === DOOR) canPlace = false;
					}
				}
			}

			if (canPlace) {
				map[y][x] = DOOR;
				doorCount++;
			}
		}

		// Add windows - not adjacent to doors or other windows
		const windowChance = 0.15;
		for (let x = 0; x < width; x++) {
			for (let y = 0; y < height; y++) {
				if (map[y][x] !== WALL) continue;
				
				// Check if window can be placed (not adjacent to door or window)
				let canPlace = true;
				for (let dy = -1; dy <= 1; dy++) {
					for (let dx = -1; dx <= 1; dx++) {
						const nx = x + dx, ny = y + dy;
						if (nx >= 0 && ny >= 0 && nx < width && ny < height) {
							if (map[ny][nx] === DOOR || map[ny][nx] === WINDOW) canPlace = false;
						}
					}
				}

				if (canPlace && rng() < windowChance) {
					map[y][x] = WINDOW;
				}
			}
		}
	}

	// 3) Add random interior wall segments (3-8 cells long, 1-5 generations based on area)
	const area = width * height;
	const segmentCount = Math.min(5, Math.max(1, Math.floor(area / 30))); // 1-5 based on area
	
	// Helper function to check if position has 2-tile clearance from existing walls
	function hasWallClearance(x: number, y: number, isHorizontal: boolean, length: number): boolean {
		const checkRadius = 2; // minimum 2-tile distance
		
		if (isHorizontal) {
			// Check horizontal segment clearance
			for (let segX = x; segX < x + length; segX++) {
				if (segX >= width) return false;
				
				// Check area around each segment cell
				for (let dy = -checkRadius; dy <= checkRadius; dy++) {
					for (let dx = -checkRadius; dx <= checkRadius; dx++) {
						const checkY = y + dy;
						const checkX = segX + dx;
						
						if (checkX >= 0 && checkY >= 0 && checkX < width && checkY < height) {
							if (map[checkY][checkX] === WALL || map[checkY][checkX] === DOOR || map[checkY][checkX] === WINDOW) {
								// Skip if it's the segment position itself
								if (checkY === y && checkX >= x && checkX < x + length) continue;
								return false;
							}
						}
					}
				}
			}
		} else {
			// Check vertical segment clearance
			for (let segY = y; segY < y + length; segY++) {
				if (segY >= height) return false;
				
				// Check area around each segment cell
				for (let dy = -checkRadius; dy <= checkRadius; dy++) {
					for (let dx = -checkRadius; dx <= checkRadius; dx++) {
						const checkY = segY + dy;
						const checkX = x + dx;
						
						if (checkX >= 0 && checkY >= 0 && checkX < width && checkY < height) {
							if (map[checkY][checkX] === WALL || map[checkY][checkX] === DOOR || map[checkY][checkX] === WINDOW) {
								// Skip if it's the segment position itself
								if (checkX === x && checkY >= y && checkY < y + length) continue;
								return false;
							}
						}
					}
				}
			}
		}
		return true;
	}
	
	for (let i = 0; i < segmentCount; i++) {
		const segmentLength = 3 + Math.floor(rng() * 6); // 3-8 cells
		const horizontal = rng() < 0.5;
		let attempts = 0;
		let placed = false;
		
		// Try up to 20 times to place segment with proper clearance
		while (!placed && attempts < 20) {
			attempts++;
			
			if (horizontal) {
				// Horizontal segment
				const startY = 3 + Math.floor(rng() * Math.max(1, height - 6)); // Leave 3-tile margin from edges
				const startX = 3 + Math.floor(rng() * Math.max(1, width - segmentLength - 6));
				
				if (hasWallClearance(startX, startY, true, segmentLength)) {
					for (let j = 0; j < segmentLength; j++) {
						const x = startX + j;
						if (x < width - 3 && map[startY][x] === NONE) {
							map[startY][x] = WALL;
						}
					}
					placed = true;
				}
			} else {
				// Vertical segment  
				const startX = 3 + Math.floor(rng() * Math.max(1, width - 6)); // Leave 3-tile margin from edges
				const startY = 3 + Math.floor(rng() * Math.max(1, height - segmentLength - 6));
				
				if (hasWallClearance(startX, startY, false, segmentLength)) {
					for (let j = 0; j < segmentLength; j++) {
						const y = startY + j;
						if (y < height - 3 && map[y][startX] === NONE) {
							map[y][startX] = WALL;
						}
					}
					placed = true;
				}
			}
		}
	}

	// 4) Fill all remaining NONE cells with debris according to weights
	for (let y = 0; y < height; y++) {
		for (let x = 0; x < width; x++) {
			if (map[y][x] === NONE) {
				const token = WEIGHTED_DEBRIS[Math.floor(rng() * WEIGHTED_DEBRIS.length)];
				map[y][x] = token;
			}
		}
	}

	// Create tokenMap
	const tokenMap = map.map(row => row.slice());

	const preset: PresetLocationMap = {
		name: `ruin_auto_${width}x${height}_${Math.floor(rng()*1e6)}`,
		width,
		height,
		tokenMap,
		anchor: { x: 0, y: 0 },
		rotations: false,
		mirror: false,
		weight: 1,
		maxInstances: 1
	};

	return preset;
}

function pickDebrisToken(rng: RNG): number {
	return WEIGHTED_DEBRIS[Math.floor(rng() * WEIGHTED_DEBRIS.length)];
}

// Определение типа поверхности из материала текстуры
function materialToTerrain(m?: MaterialTexture): TerrainType {
  switch (m) {
    case MaterialTexture.BRICK:
      return TerrainType.WOOD;
    case MaterialTexture.BETON:
      return TerrainType.BETON;
    case MaterialTexture.WOOD:
      return TerrainType.WOOD;
    case MaterialTexture.METAL:
      return TerrainType.BETON;
    default:
      return TerrainType.BETON;
  }
}

// Интерфейс для позиции руины
interface RuinPlacement {
  x: number;
  y: number;
  preset: PresetLocationMap;
}

// Функция для добавления пола под зданием/руиной
function addFloorToLocation(location: any, terrainType: TerrainType, points: [number, number][]): void {
  if (!points || points.length === 0) return;
  
  // Гарантируем наличие контейнера floor
  if (!location.floor) {
    location.floor = {} as Record<TerrainType, [number, number][]>;
    for (const t of Object.values(TerrainType) as TerrainType[]) {
      location.floor[t] = [];
    }
  }

  // Убираем эти точки из других типов поверхности
  const pointsSet = new Set(points.map(p => `${p[0]},${p[1]}`));
  for (const otherTerrain of Object.values(TerrainType) as TerrainType[]) {
    if (otherTerrain === terrainType) continue;
    if (location.floor[otherTerrain]) {
      location.floor[otherTerrain] = location.floor[otherTerrain].filter(
        (p: [number, number]) => !pointsSet.has(`${p[0]},${p[1]}`)
      );
    }
  }

  // Добавляем новые точки
  if (!location.floor[terrainType]) {
    location.floor[terrainType] = [];
  }
  location.floor[terrainType].push(...points);
}

/**
 * Универсальная функция для создания и применения руин с покраской пола
 * Может использоваться в любых локациях, не только в городах
 */
export function createAndApplyRuin(
  location: any,
  x: number, 
  y: number, 
  width: number, 
  height: number,
  legend: Record<string, string | number>,
  options: GenerateOptions = {}
): PresetLocationMap {
  // 1. Генерируем пресет руины
  const ruinPreset = generateRuinPreset(width, height, options);
  
  // 2. Применяем пресет к локации с покраской пола
  applyRuinWithFloorPainting(location, { x, y, preset: ruinPreset }, legend);
  
  return ruinPreset;
}

/**
 * Применяет руину к локации с покраской пола (основа + кантик TILES)
 */
export function applyRuinWithFloorPainting(
  location: any,
  ruinPlacement: RuinPlacement,
  legend: Record<string, string | number>
): void {
  const { x: offsetX, y: offsetY, preset } = ruinPlacement;
  const [locationWidth, locationHeight] = location.locationSize || [100, 100];
  
  // 1. Определяем базовый тип поверхности для локации
  const baseTerrain = materialToTerrain(location.textureMaterial);
  
  // 2. Собираем точки под руиной для покраски основным типом
  const ruinFloorPoints: [number, number][] = [];
  for (let y = 0; y < preset.height; y++) {
    for (let x = 0; x < preset.width; x++) {
      const targetX = offsetX + x;
      const targetY = offsetY + y;
      
      if (targetX >= 0 && targetY >= 0 && targetX < locationWidth && targetY < locationHeight) {
        ruinFloorPoints.push([targetX, targetY]);
      }
    }
  }
  
  // 3. Покрашиваем пол под руиной
  if (ruinFloorPoints.length > 0) {
    addFloorToLocation(location, baseTerrain, ruinFloorPoints);
  }
  
  // 4. Создаем кантик из TILES вокруг руины (1 клетка)
  const borderPoints: [number, number][] = [];
  const ruinPointsSet = new Set(ruinFloorPoints.map(p => `${p[0]},${p[1]}`));
  
  for (let y = -1; y <= preset.height; y++) {
    for (let x = -1; x <= preset.width; x++) {
      const targetX = offsetX + x;
      const targetY = offsetY + y;
      
      // Проверяем границы карты
      if (targetX < 0 || targetY < 0 || targetX >= locationWidth || targetY >= locationHeight) continue;
      
      // Пропускаем клетки самой руины
      if (ruinPointsSet.has(`${targetX},${targetY}`)) continue;
      
      // Проверяем, что клетка соседствует с руиной
      let isNeighbor = false;
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          if (dx === 0 && dy === 0) continue;
          const checkX = targetX + dx;
          const checkY = targetY + dy;
          if (ruinPointsSet.has(`${checkX},${checkY}`)) {
            isNeighbor = true;
            break;
          }
        }
        if (isNeighbor) break;
      }
      
      if (isNeighbor) {
        borderPoints.push([targetX, targetY]);
      }
    }
  }
  
  // 5. Покрашиваем кантик в TILES
  if (borderPoints.length > 0) {
    addFloorToLocation(location, TerrainType.TILES, borderPoints);
  }
  
  // 6. Применяем декорации руины
  applyRuinDecorations(location, ruinPlacement, legend);
}

/**
 * Применяет декорации руины к локации (без покраски пола)
 */
function applyRuinDecorations(
  location: any,
  ruinPlacement: RuinPlacement,
  legend: Record<string, string | number>
): void {
  const { x: offsetX, y: offsetY, preset } = ruinPlacement;
  
  if (!location.decorations) location.decorations = {};

  // Создаем обратное отображение: токен -> название декорации
  const tokenToDecoration: Record<number, string> = {};
  for (const [decorationName, token] of Object.entries(legend)) {
    tokenToDecoration[token as number] = decorationName;
  }

  // Применяем токены пресета
  for (let y = 0; y < preset.height; y++) {
    for (let x = 0; x < preset.width; x++) {
      const token = preset.tokenMap[y] && preset.tokenMap[y][x];
      
      if (!token || token === 999) continue;
      
      const tokenNum = typeof token === 'number' ? token : parseInt(token.toString());
      if (isNaN(tokenNum)) continue;
      
      const targetX = offsetX + x;
      const targetY = offsetY + y;
      
      const decorationName = tokenToDecoration[tokenNum];
      if (decorationName && decorationName !== 'none') {
        if (!location.decorations[decorationName]) location.decorations[decorationName] = [];
        location.decorations[decorationName].push([targetX, targetY]);
      }
    }
  }
}

export default generateRuinPreset;

/*
Примеры использования:

1. Простое создание и применение руины:
```typescript
import { createAndApplyRuin } from './ruinRND';

// В любой локации
const ruinPreset = createAndApplyRuin(
  location,     // объект локации
  10, 15,      // позиция x, y
  12, 8,       // размеры width, height
  legend,      // словарь токенов
  { rng: customRng }  // опции (необязательно)
);
```

2. Применение готового пресета руины с покраской пола:
```typescript
import { applyRuinWithFloorPainting } from './ruinRND';

const ruinPreset = generateRuinPreset(10, 12);
applyRuinWithFloorPainting(location, {
  x: 5, y: 8, preset: ruinPreset
}, legend);
```

3. Генерация пресета без применения (для последующего использования):
```typescript
const ruinPreset = generateRuinPreset(15, 10, { 
  rng: customRng,
  debrisDensity: 0.8 
});
```
*/
