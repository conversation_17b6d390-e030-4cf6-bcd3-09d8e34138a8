import { authFetch } from '../utils/authFetch';
import { UpdateProfileRequest, ChangePasswordRequest, UpdateProfileResponse, ChangePasswordResponse } from '../shared/types';

const USERS_API_URL = import.meta.env.VITE_API_BASE_URL ? `${import.meta.env.VITE_API_BASE_URL.replace(/\/$/, '')}/users` : `${location.protocol}//${location.hostname}:3001/api/users`;

export const usersApi = {
  async updateProfile(data: UpdateProfileRequest): Promise<UpdateProfileResponse> {
    const response = await authFetch(`${USERS_API_URL}/profile`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      
      throw new Error(`Failed to update profile: ${response.status} ${response.statusText}`);
    }

    const responseText = await response.text();
    
    
    if (!responseText) {
      throw new Error('Empty response from server');
    }
    
    try {
      return JSON.parse(responseText);
    } catch (e) {
     
      throw new Error('Invalid JSON response from server');
    }
  },

  async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    const response = await authFetch(`${USERS_API_URL}/password`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to change password');
    }

    return response.json();
  },
};
