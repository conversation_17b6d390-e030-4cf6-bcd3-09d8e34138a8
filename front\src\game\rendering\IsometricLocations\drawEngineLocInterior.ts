/**
 * Движок отрисовки интерьеров локаций в изометрическом стиле
 * Полностью скопирован и адаптирован под Location из WorldMap рендера
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { Location } from '../../../shared/types/Location';
import { isoToScreen } from '../../utils/coordinates/isometric';
import { drawLocationTile, drawLocationTileDecorations, locationDecorationTextureManager } from './renderUtilsLocInterior';
import { CameraRef } from '../../systems/interaction/eventHandlers';
import { getCurrentPath, highlightPathCell } from '../../systems/movement/playerMovement';
import { drawPlayerSkeleton } from '../animations/animationSystem';
import { GameTime } from '../../utils/time/gameTime';
import { applyRadialLighting, applyColorFilter } from './newLightsRender';
import { textureLoader } from '../textures/TextureLoader';
import { TerrainType } from '../../../shared/enums';
import { TERRAIN_LOCATIONS_TEXTURES, TERRAIN_TEXTURES } from '../textures';
// perfOverlay временно отключён: файл удалён, чтобы не было 404
// import { updatePerfOverlay } from '@/game/debug/perfOverlay';
import { LocationDecorations } from '../../../shared/enums';
/**
 * Основная функция отрисовки интерьера локации
 */
export const createLocationDrawFunction = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null,
  zoom: number = 1.0,
  currentLocation: Location | null = null,
  gameTime?: GameTime
) => {
  let texturesPreloaded = false;
  // Возвращаем двухпроходный рендер: 1) terrain построчно 2) декорации + игрок по диагоналям
  const frameVisibleLookup: boolean[][] = [];
  // Предварительно вычисленные позиции тайлов (isoX, isoY -> screenX, screenY относительно (0,0))
  let precomputedTilePositions: { x: number; y: number }[][] | null = null;
  // Диагонали (каждый элемент массива — массив координат тайлов на одной диагонали)
  let diagonalTiles: { isoX: number; isoY: number; x: number; y: number }[][] | null = null;
  // Предвычисленная матрица наличия декорации (для ускорения PASS2)
  let hasDecoration: boolean[][] | null = null;
  // Кэш сведений о terrain текстуре (путь + rotation + масштаб + смещения) чтобы не пересчитывать каждый кадр
  interface TerrainTexInfo { path: string | null; rotation: number; w: number; h: number; offX: number; offY: number; }
  let terrainTexCache: TerrainTexInfo[][] | null = null;
  let lastLocationKey: string | null = null;
  let frameCounter = 0;
  const statsWindow = 120; // каждые 120 кадров усредняем
  const perfAccum = { pass1: 0, pass2: 0, total: 0 };
  // --- Профилировщик ---
  // Профилировщик теперь выключен по умолчанию (включается вручную window.__toggleLocPerf())
  const ENABLE_LOC_PROFILER = false;
  interface LocPerfFrameSample {
  t: number; pass1: number; pass2: number; lighting: number; color: number; total: number;
  tilesVisible: number; tilesDrawn: number; decorations: number; playerDraws: number;
  }
  const perfState = {
    enabled: ENABLE_LOC_PROFILER,
    accum: { tilesVisible:0, tilesDrawn:0, decorations:0, playerDraws:0 },
  samples: [] as LocPerfFrameSample[],
  lastReport: 0,
  reportIntervalMs: 1000,
  summaryWindowMs: 5000,
  maxSamples: 3000
  };
  (window as any).__locPerf = perfState;
  (window as any).__toggleLocPerf = () => { perfState.enabled = !perfState.enabled; };
  // --- ЧАНКОВЫЙ КЭШ ТЕРРАИНА ---
  const CHUNK_SIZE = 8;
  const ENABLE_TERRAIN_CHUNKS = false; // временно отключаем из-за поворота/лагов
  type TerrainChunk = { canvas: HTMLCanvasElement; ctx: CanvasRenderingContext2D; isoX: number; isoY: number; dirty: boolean; offsetX: number; offsetY: number };
  const terrainChunks = new Map<string, TerrainChunk>();
  function chunkKey(cx: number, cy: number) { return cx + ':' + cy; }
  function getOrCreateChunk(cx: number, cy: number, tileWidth: number, tileHeight: number) {
    const key = chunkKey(cx, cy);
    let chunk = terrainChunks.get(key);
    if (!chunk) {
      const c = document.createElement('canvas');
      // Берём с запасом (добавляем по одному тайлу полей вокруг) — изометрическая проекция даёт сдвиги
      c.width = (CHUNK_SIZE + 2) * tileWidth;
      c.height = (CHUNK_SIZE + 2) * tileHeight;
      const cctx = c.getContext('2d')!;
      cctx.imageSmoothingEnabled = false;
      chunk = { canvas: c, ctx: cctx, isoX: cx * CHUNK_SIZE, isoY: cy * CHUNK_SIZE, dirty: true, offsetX: tileWidth, offsetY: tileHeight };
      terrainChunks.set(key, chunk);
    }
    return chunk;
  }
  function markAllTerrainDirty() { terrainChunks.forEach(ch => ch.dirty = true); }
  (window as any).__markLocationTerrainDirty = markAllTerrainDirty;

  // Локальные хелперы для terrain (дублируем, чтобы не импортировать приватные из utils)
  const LOCATION_TERRAIN_TEXTURES: Record<TerrainType, readonly string[]> = {
    [TerrainType.ASPHALT]: TERRAIN_LOCATIONS_TEXTURES.asphalt,
    [TerrainType.BETON]: TERRAIN_LOCATIONS_TEXTURES.beton,
    [TerrainType.WOOD]: TERRAIN_LOCATIONS_TEXTURES.wood,
    [TerrainType.METAL]: TERRAIN_LOCATIONS_TEXTURES.beton,
    [TerrainType.GROUND]: TERRAIN_LOCATIONS_TEXTURES.ground,
    [TerrainType.TILES]: TERRAIN_LOCATIONS_TEXTURES.tiles,
    [TerrainType.WATER]: TERRAIN_TEXTURES.water,
    [TerrainType.WASTELAND]: TERRAIN_LOCATIONS_TEXTURES.wasteland
  };
  function getLocationTexturePath(terrain: TerrainType, x: number, y: number): string | null {
    const list = LOCATION_TERRAIN_TEXTURES[terrain];
    if (!list || !list.length) return null;
    const p = (x * Math.PI + y * Math.PI * 2.71828);
    const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
    const idx = Math.floor(hash) % list.length;
    return list[idx];
  }
  function drawTerrainTileInto(ctx: CanvasRenderingContext2D, isoX: number, isoY: number, baseIsoX: number, baseIsoY: number, chunk: TerrainChunk, tileWidth: number, tileHeight: number, location: any) {
    const tileKey = `${isoX},${isoY}`;
    const tile = location?.locationMap?.[tileKey];
    if (!tile) return;
    const pos = isoToScreen(isoX, isoY, tileWidth, tileHeight);
    const basePos = isoToScreen(baseIsoX, baseIsoY, tileWidth, tileHeight);
    const centerX = (pos.x - basePos.x) + chunk.offsetX;
    const centerY = (pos.y - basePos.y) + chunk.offsetY;
    const halfTileW = tileWidth / 2;
    const halfTileH = tileHeight / 2;
    let drewTexture = false;
    if (tile.terrain) {
      const path = getLocationTexturePath(tile.terrain, isoX, isoY);
      if (path) {
        const img = textureLoader.getTexture(path);
        if (img && img.complete && img.width > 1) {
          // фиксированный 80x80 как в исходном рендере
          ctx.drawImage(img, centerX - 40, centerY - 40, 80, 80);
          drewTexture = true;
        }
      }
    }
    if (!drewTexture) {
      // Fallback romb fill
      ctx.fillStyle = '#8B4513';
      ctx.beginPath();
      ctx.moveTo(centerX, centerY - halfTileH);
      ctx.lineTo(centerX + halfTileW, centerY);
      ctx.lineTo(centerX, centerY + halfTileH);
      ctx.lineTo(centerX - halfTileW, centerY);
      ctx.closePath();
      ctx.fill();
    }
  }

  function rebuildChunk(chunk: TerrainChunk, location: any, tileWidth: number, tileHeight: number, locationSize: {x:number;y:number}) {
    const { ctx, isoX, isoY, canvas } = chunk;
    ctx.clearRect(0,0,canvas.width, canvas.height);
    // перерисовываем внутренние тайлы чанка
    for (let y = 0; y < CHUNK_SIZE; y++) {
      for (let x = 0; x < CHUNK_SIZE; x++) {
        const gx = isoX + x;
        const gy = isoY + y;
        if (gx >= locationSize.x || gy >= locationSize.y) continue;
        drawTerrainTileInto(ctx, gx, gy, isoX, isoY, chunk, tileWidth, tileHeight, location);
      }
    }
    chunk.dirty = false;
  }
  
  return () => {
    const canvas = canvasRef.current;
    if (!canvas || !cameraRef.current) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Предзагружаем текстуры локаций только один раз
    if (!texturesPreloaded) {
      locationDecorationTextureManager.preloadAllTextures();
      texturesPreloaded = true;
    }

    // Сохраняем состояние контекста
    ctx.save();

    ctx.imageSmoothingEnabled = false;

    // Очищаем канвас
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Применяем зум
    ctx.translate(canvasWidth / 2, canvasHeight / 2);
    ctx.scale(zoom, zoom);
    ctx.translate(-canvasWidth / 2, -canvasHeight / 2);

    // Определяем позицию игрока для рендера локации: если есть currentLocation, используем его локальную позицию
    const playerPos = currentLocation?.playerPosition ?? currentWorld?.player?.position;
    // currentWorld?.worldMap проверяем только для world map рендеринга; в локации нам важна currentLocation
    if (!playerPos) {
      ctx.restore();
      return;
    }

    // Получаем локацию из параметра (уже сконвертированную)
    if (!currentLocation) {
      
      return;
    }

  const location = currentLocation;

    const locationSize = location.locationSize || { x: 10, y: 10 };

    // Центр экрана
    const centerX = cameraRef.current.x;
    const centerY = cameraRef.current.y;

    // Подготовка: если сменился размер / другая локация — пересчитать таблицы
    const locationKey = `${locationSize.x}x${locationSize.y}`;
    if (!precomputedTilePositions || !diagonalTiles || lastLocationKey !== locationKey) {
      precomputedTilePositions = Array.from({ length: locationSize.y }, () => new Array(locationSize.x));
      for (let y = 0; y < locationSize.y; y++) {
        for (let x = 0; x < locationSize.x; x++) {
          precomputedTilePositions[y][x] = isoToScreen(x, y, tileWidth, tileHeight);
        }
      }
      // Диагонали
      const maxDiagonalIdx = (locationSize.x - 1) + (locationSize.y - 1);
      diagonalTiles = [];
      for (let d = 0; d <= maxDiagonalIdx; d++) {
        const list: { isoX: number; isoY: number; x: number; y: number }[] = [];
        for (let isoX = 0; isoX < locationSize.x; isoX++) {
          const isoY = d - isoX;
          if (isoY >= 0 && isoY < locationSize.y) {
            const pos = precomputedTilePositions[isoY][isoX];
            list.push({ isoX, isoY, x: pos.x, y: pos.y });
          }
        }
        diagonalTiles.push(list);
      }
      // Матрица декораций
      hasDecoration = Array.from({ length: locationSize.y }, () => new Array<boolean>(locationSize.x).fill(false));
      // Кэш terrain текстур
      terrainTexCache = Array.from({ length: locationSize.y }, () => new Array<TerrainTexInfo>(locationSize.x));
      // Заполняем кэш (используем ту же детерминированную функцию что и в drawLocationTile)
      for (let y = 0; y < locationSize.y; y++) {
        for (let x = 0; x < locationSize.x; x++) {
          const tileKey = `${x},${y}`;
          const tile = (location as any)?.locationMap?.[tileKey];
          if (!tile || !tile.terrain) {
            terrainTexCache[y][x] = { path: null, rotation: 0, w: 80, h: 80, offX: 0, offY: 0 };
            continue;
          }
          const path = getLocationTexturePath(tile.terrain, x, y);
          // Rotation / scaling / offset будут рассчитаны позже (в момент использования) — здесь сохраняем базу
          terrainTexCache[y][x] = { path, rotation: 0, w: 80, h: 80, offX: 0, offY: 0 };
        }
      }
      if (location?.locationMap) {
        for (const key in location.locationMap) {
          const entry = location.locationMap[key];
          if (!entry) continue;
          if (entry.decoration && entry.decoration !== LocationDecorations.NONE && entry.decoration !== LocationDecorations.VOID) {
            const [sx, sy] = key.split(',').map(n=>parseInt(n,10));
            if (!Number.isNaN(sx) && !Number.isNaN(sy) && sy < locationSize.y && sx < locationSize.x) {
              hasDecoration![sy][sx] = true;
            }
          }
        }
      }
      lastLocationKey = locationKey;
    }

  let frameStart = performance.now();
  let t0 = frameStart;
    let t1 = t0;
    if (ENABLE_TERRAIN_CHUNKS) {
      // текущая (пока отключенная) реализация чанков
      for (let y = 0; y < locationSize.y; y++) { if (!frameVisibleLookup[y]) frameVisibleLookup[y] = []; }
      const maxChunkX = Math.ceil(locationSize.x / CHUNK_SIZE);
      const maxChunkY = Math.ceil(locationSize.y / CHUNK_SIZE);
      for (let cy = 0; cy < maxChunkY; cy++) {
        for (let cx = 0; cx < maxChunkX; cx++) {
          const chunk = getOrCreateChunk(cx, cy, tileWidth, tileHeight);
          if (chunk.dirty) rebuildChunk(chunk, location, tileWidth, tileHeight, locationSize);
          const firstIsoX = chunk.isoX;
          const firstIsoY = chunk.isoY;
          if (firstIsoX < locationSize.x && firstIsoY < locationSize.y) {
            const basePos = precomputedTilePositions![firstIsoY][firstIsoX];
            const worldX = basePos.x + canvasWidth / 2 - centerX;
            const worldY = basePos.y + canvasHeight / 2 - centerY;
            if (worldX < canvasWidth + tileWidth && worldY < canvasHeight + tileHeight && worldX > -chunk.canvas.width - tileWidth && worldY > -chunk.canvas.height - tileHeight) {
              ctx.drawImage(chunk.canvas, worldX - chunk.offsetX, worldY - chunk.offsetY);
              for (let ly = 0; ly < CHUNK_SIZE; ly++) {
                for (let lx = 0; lx < CHUNK_SIZE; lx++) {
                  const gx = firstIsoX + lx;
                  const gy = firstIsoY + ly;
                  if (gx >= locationSize.x || gy >= locationSize.y) continue;
                  const tilePos = precomputedTilePositions![gy][gx];
                  const tWorldX = tilePos.x + canvasWidth / 2 - centerX;
                  const tWorldY = tilePos.y + canvasHeight / 2 - centerY;
                  const visible = (tWorldX >= -tileWidth && tWorldX <= canvasWidth + tileWidth && tWorldY >= -tileHeight && tWorldY <= canvasHeight + tileHeight);
                  frameVisibleLookup[gy][gx] = visible;
                }
              }
            } else {
              for (let ly = 0; ly < CHUNK_SIZE; ly++) {
                for (let lx = 0; lx < CHUNK_SIZE; lx++) {
                  const gx = firstIsoX + lx;
                  const gy = firstIsoY + ly;
                  if (gx >= locationSize.x || gy >= locationSize.y) continue;
                  frameVisibleLookup[gy][gx] = false;
                }
              }
            }
          }
        }
      }
      t1 = performance.now();
    } else {
      // Оригинальный PASS1 построчно (с предвычисленными позициями) для корректного поворота/клиппа через drawLocationTile
      for (let y = 0; y < locationSize.y; y++) { if (!frameVisibleLookup[y]) frameVisibleLookup[y] = []; }
  for (let isoY = 0; isoY < locationSize.y; isoY++) {
    for (let isoX = 0; isoX < locationSize.x; isoX++) {
          const { x: screenX, y: screenY } = precomputedTilePositions![isoY][isoX];
            const worldX = screenX + canvasWidth / 2 - centerX;
            const worldY = screenY + canvasHeight / 2 - centerY;
            if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
              frameVisibleLookup[isoY][isoX] = true;
      if (perfState.enabled) perfState.accum.tilesVisible++;
              drawLocationTile(
                ctx,
                screenX,
                screenY,
                isoX,
                isoY,
                tileWidth,
                tileHeight,
                canvasWidth,
                canvasHeight,
                centerX,
                centerY,
                location,
                cellTarget,
                false,
                false
              );
              if (perfState.enabled) perfState.accum.tilesDrawn++;
            } else {
              frameVisibleLookup[isoY][isoX] = false;
            }
        }
      }
      t1 = performance.now();
    }

    // Получаем позицию игрока и определяем анимацию
    let animationName = 'scratching-right';
    
    if (playerPos && window.__playerIsMoving && window.__playerMoveDirection) {
      // Логика анимации (как было раньше)
      const { dx, dy } = window.__playerMoveDirection;
      
      const playerX = playerPos.x;
      const playerY = playerPos.y;

      const targetX = playerX + dx;
      const targetY = playerY + dy;
      
  const playerScreen = isoToScreen(playerX, playerY, tileWidth, tileHeight);
  const targetScreen = isoToScreen(targetX, targetY, tileWidth, tileHeight);
      
      const screenDeltaX = targetScreen.x - playerScreen.x;
      const screenDeltaY = targetScreen.y - playerScreen.y;
      
      const angle = Math.atan2(screenDeltaY, screenDeltaX);
      
      let normalizedAngle = angle < 0 ? angle + 2 * Math.PI : angle;
      
      let adjustedAngle = normalizedAngle - Math.PI / 2;
      if (adjustedAngle < 0) adjustedAngle += 2 * Math.PI;
      
      const degrees = (adjustedAngle * 180) / Math.PI;
      
      let skewYCorrection = 0;
      const maxSkewIntensity = 0.14;
      
      if ((degrees >= 335 && degrees <= 360) || (degrees >= 0 && degrees < 25)) {
        animationName = 'walk_south';
        window.__playerDirection = 'south';
        skewYCorrection = 0;
      } else if (degrees >= 25 && degrees < 90) {
        animationName = 'walk_south_west';
        window.__playerDirection = 'south';
        const distanceFromSouth = Math.min(degrees - 25, 65) / 65;
        skewYCorrection = distanceFromSouth * maxSkewIntensity;
      } else if (degrees >= 90 && degrees < 155) {
        animationName = 'walk_north_west';
        window.__playerDirection = 'west';
        skewYCorrection = maxSkewIntensity;
      } else if (degrees >= 155 && degrees < 205) {
        animationName = 'walk_north';
        window.__playerDirection = 'north';
        skewYCorrection = 0;
      } else if (degrees >= 205 && degrees < 270) {
        animationName = 'walk_north_east';
        window.__playerDirection = 'north';
        const distanceFromNorth = Math.min(degrees - 205, 65) / 65;
        skewYCorrection = -distanceFromNorth * maxSkewIntensity;
      } else if (degrees >= 270 && degrees < 335) {
        animationName = 'walk_south_east';
        window.__playerDirection = 'east';
        skewYCorrection = -maxSkewIntensity;
      }
      
      window.__playerSkewYCorrection = skewYCorrection;
    } else {
      window.__playerDirection = 'idle';
    }

    // PASS 2: диагональный порядок (используем предвычисленные диагонали)
    const t2 = performance.now();
    for (const diag of diagonalTiles!) {
      for (const tile of diag) {
        if (!frameVisibleLookup[tile.isoY][tile.isoX]) continue;
        const isPlayerTile = playerPos && playerPos.x === tile.isoX && playerPos.y === tile.isoY;
        if (!isPlayerTile && hasDecoration && !hasDecoration[tile.isoY][tile.isoX]) continue; // пропускаем пустые
        if (hasDecoration && hasDecoration[tile.isoY][tile.isoX]) {
          drawLocationTileDecorations(
            ctx,
            tile.x,
            tile.y,
            tile.isoX,
            tile.isoY,
            tileWidth,
            tileHeight,
            canvasWidth,
            canvasHeight,
            centerX,
            centerY,
            location,
            playerPos,
            cellTarget
          );
          if (perfState.enabled) perfState.accum.decorations++;
        }
        if (isPlayerTile) {
          if (typeof drawPlayerSkeleton === 'function') {
            const worldForRender = { ...currentWorld } as any;
            if (worldForRender) {
              worldForRender.player = { ...(worldForRender.player || {}), position: playerPos };
            }
            drawPlayerSkeleton(
              ctx,
              tileWidth,
              tileHeight,
              canvasWidth,
              canvasHeight,
              centerX,
              centerY,
              worldForRender,
              animationName
            );
            if (perfState.enabled) perfState.accum.playerDraws++;
          }
        }
      }
    }
    const t3 = performance.now();
    // Сбор минимальной статистики производительности (без лишних логов)
    perfAccum.pass1 += (t1 - t0);
    perfAccum.pass2 += (t3 - t2);
    perfAccum.total += (t3 - t0);
    frameCounter++;
    if (frameCounter % statsWindow === 0) {
      (window as any).__locFrameStats = {
        avgPass1: +(perfAccum.pass1 / statsWindow).toFixed(3),
        avgPass2: +(perfAccum.pass2 / statsWindow).toFixed(3),
        avgTotal: +(perfAccum.total / statsWindow).toFixed(3),
        tileCount: locationSize.x * locationSize.y,
      };
      perfAccum.pass1 = perfAccum.pass2 = perfAccum.total = 0;
    }

  // ЭТАП 2: UI
    
    // Рис��ем центральную точку для ориентации
    ctx.fillStyle = '#ff353500';
    ctx.beginPath();
    ctx.arc(canvasWidth / 2, canvasHeight / 2, 3, 0, 2 * Math.PI);
    ctx.fill();

    // Рисуем крестики для пути персонажа (всегда показываем в локации)
    const currentPath = getCurrentPath();
    for (const pathPos of currentPath) {
      const { x: screenX, y: screenY } = isoToScreen(pathPos.x, pathPos.y, tileWidth, tileHeight);
      const worldX = screenX + canvasWidth / 2 - centerX;
      const worldY = screenY + canvasHeight / 2 - centerY;

      // Рисуем крестик только если тайл видим
      if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
        highlightPathCell(ctx, worldX, worldY, tileWidth, tileHeight);
      }
    }

    // Восстанавливаем состояние контекста
    ctx.restore();

    const lightStart = performance.now();
    // ЭТАП 5: РАДИАЛЬНОЕ ОСВЕЩЕНИЕ
    applyRadialLighting(
      ctx, 
      canvasWidth, 
      canvasHeight, 
      gameTime, 
      currentLocation, 
      currentWorld, 
      playerPos, 
      cameraRef, 
      tileWidth, 
      tileHeight, 
      zoom
    );
    const lightEnd = performance.now();
    const colorStart = lightEnd;
    // ЭТАП 6: ПРИМЕНЯЕМ ЦВЕТОВОЙ ФИЛЬТР
    applyColorFilter(ctx, canvasWidth, canvasHeight, gameTime, currentLocation);
    const colorEnd = performance.now();

  if (perfState.enabled) {
      const now = performance.now();
      const sample: LocPerfFrameSample = {
        t: now,
        pass1: +(t1 - t0),
        pass2: +(t3 - t2),
        lighting: +(lightEnd - lightStart),
        color: +(colorEnd - colorStart),
        total: +(colorEnd - frameStart),
        tilesVisible: perfState.accum.tilesVisible,
        tilesDrawn: perfState.accum.tilesDrawn,
        decorations: perfState.accum.decorations,
        playerDraws: perfState.accum.playerDraws
      };
      perfState.samples.push(sample);
    // Обновляем overlay (если включен)
    try {
  // Обновляем overlay только если он включён (глобал выставляется perfOverlay.ts)
  const summary = (window as any).__locPerfSummary;
  // if (summary && (window as any).__perfOverlayEnabled) updatePerfOverlay(summary);
    } catch {}
      // ограничиваем массив
      if (perfState.samples.length > perfState.maxSamples) {
        perfState.samples.splice(0, perfState.samples.length - perfState.maxSamples);
      }
      // reset accum
      perfState.accum.tilesVisible = perfState.accum.tilesDrawn = 0;
      perfState.accum.decorations = perfState.accum.playerDraws = 0;
      if (now - perfState.lastReport > perfState.reportIntervalMs) {
        const cutoff = now - perfState.summaryWindowMs;
        const windowSamples = perfState.samples.filter(s => s.t >= cutoff);
        const agg = (fn: (s: LocPerfFrameSample)=>number) => windowSamples.reduce((a,s)=>a+fn(s),0);
        const len = windowSamples.length || 1;
        const avg = (fn: (s: LocPerfFrameSample)=>number) => +(agg(fn)/len).toFixed(3);
        const max = (fn: (s: LocPerfFrameSample)=>number) => +Math.max(...windowSamples.map(fn)).toFixed(3);
        const min = (fn: (s: LocPerfFrameSample)=>number) => +Math.min(...windowSamples.map(fn)).toFixed(3);
        const p95 = (fn: (s: LocPerfFrameSample)=>number) => {
          if (!windowSamples.length) return 0;
            const arr = windowSamples.map(fn).sort((a,b)=>a-b);
            return +arr[Math.min(arr.length-1, Math.floor(arr.length*0.95))].toFixed(3);
        };
        (window as any).__locPerfSummary = {
          windowMs: perfState.summaryWindowMs,
          frames: windowSamples.length,
          fpsApprox: +(1000 * windowSamples.length / perfState.summaryWindowMs).toFixed(1),
          pass1: avg(s=>s.pass1), pass2: avg(s=>s.pass2), lighting: avg(s=>s.lighting), color: avg(s=>s.color), total: avg(s=>s.total),
          pass1_max: max(s=>s.pass1), pass2_max: max(s=>s.pass2), total_max: max(s=>s.total),
          total_p95: p95(s=>s.total),
          tilesVisible_avg: avg(s=>s.tilesVisible), decorations_avg: avg(s=>s.decorations)
        };
        perfState.lastReport = now;
      }
      // helper для ручного получения сводки за X секунд
      (window as any).__locPerfGetSummary = (seconds: number = 5) => {
        const cutoff2 = performance.now() - seconds*1000;
        const subset = perfState.samples.filter(s=>s.t >= cutoff2);
        if (!subset.length) return null;
        const len2 = subset.length;
        const avg2 = (fn:(s:LocPerfFrameSample)=>number)=> +(subset.reduce((a,s)=>a+fn(s),0)/len2).toFixed(3);
        return {
          seconds,
          frames: len2,
          fpsApprox: +(1000*len2/(seconds*1000)).toFixed(1),
          pass1: avg2(s=>s.pass1), pass2: avg2(s=>s.pass2), total: avg2(s=>s.total)
        };
      };
    }
  };
};