import { WorldMapCell } from '../../shared/types/World';
import { locationConfigs, LocationConfig } from './constants/locationConfig';
import { generateGridForLocation } from './generateGridForLocation';
import { placePresetsForLocation } from './placePresetForLocation';
import tokenLegend from './presets/tokenLegend';
import declareDecorationSides, { declareStairsOrientation } from './sideDeclarator';
import { runCleaningPasses } from './cleaningLocation';
import setLocationFloor from './setLocationFloor';
import { generateStreetsForLocation } from './streetsGenerator';
import { CancellationToken } from '../../utils/asyncUtils';

// tokenLegend values are numeric constants, allow number|string
const legend = tokenLegend as Record<string, string | number>;

// Основная асинхронная функция генерации контента для всех локаций
export async function generateLocationContentDataAsync(
  worldMap: { worldMapCells: WorldMapCell[] },
  rng: () => number,
  progressCallback?: (progress: number, stage: string, operation: string) => void,
  cancellationToken?: CancellationToken
): Promise<void> {
	const cells = worldMap.worldMapCells.filter(cell => cell.location);
	const total = cells.length;

	if (total === 0) {
		
		return;
	}

	// Простая система прогресса: от 20% до 100% (интерьеры занимают 80% общего времени)
	let processed = 0;



	// Простая последовательная обработка локаций
	let totalGridTime = 0;
	let totalStreetsTime = 0;
	let totalPresetsTime = 0;
	let totalPostTime = 0;

	for (let i = 0; i < total; i++) {
		if (cancellationToken?.isCancelled) {
			throw new Error('Operation was cancelled');
		}

		const cell = cells[i];
		const location = cell.location!;
		const subtype = location.subtype;
		const config = locationConfigs[subtype];

		const locationStartTime = Date.now();
		const timings = await generateLocationInterior(cell, config, rng);
		const locationEndTime = Date.now();

		// Аккумулируем времена по этапам
		totalGridTime += timings.gridTime;
		totalStreetsTime += timings.streetsTime;
		totalPresetsTime += timings.presetsTime;
		totalPostTime += timings.postTime;

		processed++;

		// Отправляем прогресс пользователю (20% + 70% * прогресс интерьеров = 20-90%)
		if (progressCallback) {
			const interiorProgress = 20 + Math.floor((processed / total) * 70);
			progressCallback(interiorProgress, 'Создание интерьеров', `Обработано ${processed} из ${total} локаций`);
		}

		// Логируем прогресс каждые 20 локаций
		if (processed % 20 === 0) {
			console.log(`📊 Обработано ${processed}/${total} локаций. Средние времена: grid=${(totalGridTime/processed).toFixed(1)}мс, streets=${(totalStreetsTime/processed).toFixed(1)}мс, presets=${(totalPresetsTime/processed).toFixed(1)}мс, post=${(totalPostTime/processed).toFixed(1)}мс`);
		}

		// Освобождаем event loop каждые 5 локаций
		if (processed % 5 === 0) {
			await new Promise(res => setImmediate(res));
		}
	}

	// Финальная статистика
	console.log(`📈 Финальная статистика интерьеров:`);
	console.log(`   🏗️ Общее время grid: ${(totalGridTime / 1000).toFixed(2)}с (среднее: ${(totalGridTime/total).toFixed(1)}мс на локацию)`);
	console.log(`   🛣️ Общее время streets: ${(totalStreetsTime / 1000).toFixed(2)}с (среднее: ${(totalStreetsTime/total).toFixed(1)}мс на локацию)`);
	console.log(`   🎨 Общее время presets: ${(totalPresetsTime / 1000).toFixed(2)}с (среднее: ${(totalPresetsTime/total).toFixed(1)}мс на локацию)`);
	console.log(`   🧹 Общее время post: ${(totalPostTime / 1000).toFixed(2)}с (среднее: ${(totalPostTime/total).toFixed(1)}мс на локацию)`);

  
 
}

// Пошаговая генерация интерьера одной локации
async function generateLocationInterior(
	cell: WorldMapCell,
	config: LocationConfig,
	rng: () => number
): Promise<{ gridTime: number; streetsTime: number; presetsTime: number; postTime: number }> {
	const locationStartTime = Date.now();

	// Шаг 1: Генерация грида и базового terrain
	const gridStartTime = Date.now();
	await generateGridForLocation(cell, config, rng);
	const gridTime = Date.now() - gridStartTime;

	// Шаг 2: Генерация улиц
	const streetsStartTime = Date.now();
	await generateStreetsForLocation(cell.location!, config, rng);
	const streetsTime = Date.now() - streetsStartTime;

	// Шаг 3: Размещение пресетов (ОСНОВНАЯ НАГРУЗКА)
	const presetsStartTime = Date.now();
	await placePresetsForLocation(cell, config, rng, legend);
	const presetsTime = Date.now() - presetsStartTime;

	// Шаг 4: Очистка и постобработка
	const postStartTime = Date.now();
	declareStairsOrientation(cell.location!);
	setLocationFloor(cell.location!);
	await runCleaningPasses(cell.location!);
	declareDecorationSides(cell.location!);
	const postTime = Date.now() - postStartTime;

	const totalTime = Date.now() - locationStartTime;

	// Логируем время только для некоторых локаций, чтобы не засорять лог
	if (Math.random() < 0.05) { // 5% шанс логирования
		console.log(`⏱️ Локация ${cell.location?.name}: grid=${gridTime}мс, streets=${streetsTime}мс, presets=${presetsTime}мс, post=${postTime}мс, total=${totalTime}мс`);
	}

	return { gridTime, streetsTime, presetsTime, postTime };
}