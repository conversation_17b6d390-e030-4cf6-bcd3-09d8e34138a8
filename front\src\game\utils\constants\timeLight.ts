/**
 * Константы времени и освещения для игровой системы
 */

// Константы времени
export const TIME_CONSTANTS = {
  REAL_MINUTES_PER_GAME_DAY: 24, // 24 реальных минуты = 1 игровой день
  REAL_SECONDS_PER_GAME_HOUR: 60, // 60 реальных секунд = 1 игровой час
  REAL_MS_PER_GAME_MINUTE: 1000, // 1000 реальных мс = 1 игровая минута
  REAL_MS_PER_GAME_SECOND: 16.67, // ~16.67 реальных мс = 1 игровая секунда (для плавности)
  UPDATE_INTERVAL_MS: 100, // Обновляем каждые 100мс для плавных переходов
  
  // Время рассвета и заката
  SUNRISE_HOUR: 6,
  SUNSET_HOUR: 18,
  
  // Полные циклы
  HOURS_PER_DAY: 24,
  MINUTES_PER_HOUR: 60,
  SECONDS_PER_MINUTE: 60,
  DAYS_PER_SEASON: 30
} as const;

// Константы освещения по времени суток
export const LIGHT_CONSTANTS = {
  // Уровни освещенности (0-1)
  DEEP_NIGHT_LEVEL: 0.05,     // 22:00-5:00 - почти полная темнота
  PREDAWN_START: 0.05,        // 5:00 - начало предрассветных сумерек
  PREDAWN_END: 0.4,           // 6:00 - конец предрассветных сумерек
  DAWN_START: 0.4,            // 6:00 - начало рассвета
  DAWN_END: 1.0,              // 8:00 - конец рассвета, полный день
  FULL_DAY_LEVEL: 1.0,        // 8:00-17:00 - максимальное освещение
  SUNSET_START: 1.0,          // 17:00 - начало заката
  SUNSET_END: 0.7,            // 19:00 - конец заката
  TWILIGHT_START: 0.7,        // 19:00 - начало сумерек
  TWILIGHT_END: 0.3,          // 21:00 - конец сумерек
  LATE_TWILIGHT_START: 0.3,   // 21:00 - поздние сумерки
  LATE_TWILIGHT_END: 0.05,    // 22:00 - переход к ночи
  
  // Минимальный уровень света для дневной видимости
  DAYTIME_THRESHOLD: 0.6,
  
  // Переходные периоды (часы)
  PREDAWN_DURATION: 1,        // 5:00-6:00
  DAWN_DURATION: 2,           // 6:00-8:00
  SUNSET_DURATION: 2,         // 17:00-19:00
  TWILIGHT_DURATION: 2,       // 19:00-21:00
  LATE_TWILIGHT_DURATION: 1   // 21:00-22:00
} as const;

// Константы видимости
export const VISION_CONSTANTS = {
  // Базовые множители радиуса видимости
  NIGHT_VISION_MULTIPLIER: 1.5,    // perception * 3 ночью
  MAX_DAY_VISION_RADIUS: 40,    // практически неограниченная видимость днем
  MIN_VISION_RADIUS: 1,          // минимальный радиус видимости
  
  // Параметры ореола затемнения
  DARKNESS_HALO_RADIUS: 3,       // радиус плавного перехода в клетках
  MAX_DARKNESS_LEVEL: 0.9,       // максимальное затемнение (90%)
  HALO_DARKNESS_LEVEL: 0.8,      // затемнение в ореоле (80%)
  MIN_VISIBILITY: 0.03,           // минимальная видимость (10%)
  
  // Переходы освещения
  LIGHT_TRANSITION_MIN: 0.05,    // минимальный уровень для расчета переходов
  LIGHT_TRANSITION_RANGE: 0.75   // диапазон перехода (0.05 - 0.6)
} as const;

// Константы освещения от декораций
export const DECORATION_LIGHT_CONSTANTS = {
  LIGHTS_ON: true,
  // Радиусы освещения для разных типов светильников
  STREETLIGHT_RADIUS: 10,         // Уличные фонари
  EXTERIOR_LIGHT_RADIUS: 10,      // Внешние светильники
  INTERIOR_LIGHT_RADIUS: 6,     // Внутренние светильники

  // Радиусы ореола (плавного влияния света за пределами основного радиуса)
  STREETLIGHT_HALO_RADIUS: 3,
  EXTERIOR_LIGHT_HALO_RADIUS: 2,
  INTERIOR_LIGHT_HALO_RADIUS: 2,
  
  // Интенсивность освещения (0-1)
  STREETLIGHT_INTENSITY: 0.8,    // 80% - яркие уличные фонари
  EXTERIOR_LIGHT_INTENSITY: 0.6, // 60% - умеренные внешние светильники
  INTERIOR_LIGHT_INTENSITY: 0.9, // 90% - яркие внутренние светильники
  
  // Падение яркости с расстоянием
  LIGHT_FALLOFF_POWER: 3,        // Квадратичное падение света
  MIN_LIGHT_LEVEL: 0.08,          // Минимальный уровень освещения
  MAX_LIGHT_LEVEL: 1.0,          // Максимальный уровень освещения
  
  // Цвета освещения для разных типов светильников
  STREETLIGHT_COLOR: { r: 255, g: 220, b: 180 },    // Теплый желтоватый свет
  EXTERIOR_LIGHT_COLOR: { r: 255, g: 240, b: 200 }, // Слегка холодноватый белый
  INTERIOR_LIGHT_COLOR: { r: 255, g: 250, b: 220 }  // Теплый домашний свет
} as const;

// Временные периоды дня
export const TIME_PERIODS = {
  DEEP_NIGHT: { start: 22, end: 5 },      // Глубокая ночь
  PREDAWN: { start: 5, end: 6 },          // Предрассветные сумерки
  DAWN: { start: 6, end: 8 },             // Рассвет
  FULL_DAY: { start: 8, end: 17 },        // Полный день
  SUNSET: { start: 17, end: 19 },         // Закат
  TWILIGHT: { start: 19, end: 21 },       // Сумерки
  LATE_TWILIGHT: { start: 21, end: 22 }   // Поздние сумерки
} as const;

// Цвета освещения для разных времен дня
export const LIGHT_COLORS = {
  DAWN: { r: 255, g: 180, b: 120, a: 0.2 },        // Яркий оранжевый рассвет
  DAY: { r: 255, g: 255, b: 255, a: 0.0 },         // Белый день (без фильтра)
  SUNSET: { r: 255, g: 120, b: 80, a: 0.1 },       // Насыщенный красно-оранжевый закат
  TWILIGHT: { r: 40, g: 40, b: 50, a: 0.3 },     // Глубокие синие сумерки
  NIGHT: { r: 20, g: 20, b: 30, a: 0.3 }          // Темно-синяя ночь
} as const;

// Постоянный цветофильтр для интерьеров
export const INTERIOR_COLOR_FILTER = {
  r: 15, g: 20, b: 30, a: 0.2  // Теплый желтоватый фильтр для интерьеров
} as const;
