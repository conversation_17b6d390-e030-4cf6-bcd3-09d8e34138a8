/**
 * Типы звуковых каналов и интерфейсы
 */

export enum SoundChannel {
  MASTER = 'master',        // общая громкость
  EFFECTS = 'effects',      // выстрелы, взрывы, события
  AMBIENT = 'ambient',      // атмосфера
  DIALOG = 'dialog',        // диалоги (пока закомичено)
  UI = 'ui'                 // нажатия кнопок меню
}

export interface SoundSettings {
  volumes: Record<SoundChannel, number>  // 0-100
  muted: Record<SoundChannel, boolean>
  globalMute: boolean
}

export interface PlaySoundOptions {
  loop?: boolean
  volume?: number  // относительная громкость 0-1
  fadeIn?: number  // мс
  onEnd?: () => void
}

export interface ActiveSound {
  id: string
  audio: HTMLAudioElement
  channel: SoundChannel
  volume: number
  isLooping: boolean
}
