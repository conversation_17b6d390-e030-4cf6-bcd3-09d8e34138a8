// ===== SPRITE-BASED LIGHT HALO (pre-rendered) =====
type LightSpriteKey = string;
const lightSpriteCache = new Map<LightSpriteKey, HTMLCanvasElement>();

// Persistent off-screen canvases (static + dynamic placeholders for future). Now only one.
let lightMaskCanvas: HTMLCanvasElement | null = null;
let lightMaskCtx: CanvasRenderingContext2D | null = null;
let lastCanvasW = 0;
let lastCanvasH = 0;
// Track last location id/name to know when to reset caches
let lastLocationKey: string | null = null;

/**
 * Reset lighting caches (call при смене локации / генерации новой)
 */
export function resetLightingCache() {
  lightSpriteCache.clear();
  // сбрасываем маску чтобы пересоздать под новый размер при следующем кадре
  lightMaskCanvas = null;
  lightMaskCtx = null;
  lastLocationKey = null;
}

// ПРИМЕЧАНИЕ ПО ИСПОЛЬЗОВАНИЮ:
// 1. Вызывайте resetLightingCache() после генерации новой случайной локации или при явном выходе из неё.
// 2. Внутри applyRadialLighting есть автоматическое определение смены локации (по id/name) и сброс.
//    Если у локации нет стабильного id/name – обеспечьте его или вызывайте resetLightingCache вручную.

function ensureLightMaskCanvas(w: number, h: number) {
  if (!lightMaskCanvas || !lightMaskCtx || w !== lastCanvasW || h !== lastCanvasH) {
    lightMaskCanvas = document.createElement('canvas');
    lightMaskCanvas.width = w;
    lightMaskCanvas.height = h;
    lightMaskCtx = lightMaskCanvas.getContext('2d');
    lastCanvasW = w;
    lastCanvasH = h;
  }
  return lightMaskCtx!;
}

function getLightSprite(radius: number, ellipseX: number, ellipseY: number): HTMLCanvasElement {
  // Квантизация радиуса чтобы уменьшить число вариантов спрайтов в кэше (визуально незаметно)
  const RADIUS_QUANT = 4; // пикселей; при необходимости вынести в конфиг
  radius = Math.max(1, Math.round(radius / RADIUS_QUANT) * RADIUS_QUANT);
  // Ключ кэша по параметрам
  const key = `${radius}_${ellipseX}_${ellipseY}`;
  if (lightSpriteCache.has(key)) return lightSpriteCache.get(key)!;

  // Размер спрайта (с запасом)
  const w = Math.ceil(radius * ellipseX * 2);
  const h = Math.ceil(radius * ellipseY * 2);
  const cx = w / 2;
  const cy = h / 2;

  const sprite = document.createElement('canvas');
  sprite.width = w;
  sprite.height = h;
  const ctx = sprite.getContext('2d')!;

  // Эллиптический градиент
  ctx.save();
  ctx.translate(cx, cy);
  ctx.scale(ellipseX, ellipseY);
  ctx.translate(-cx, -cy);
  const grad = ctx.createRadialGradient(cx, cy, 0, cx, cy, radius);
  grad.addColorStop(0, `rgba(255,255,255,${LIGHT_HALO_SETTINGS.INNER_INTENSITY})`);
  grad.addColorStop(LIGHT_HALO_SETTINGS.INNER_LIGHT_RADIUS, `rgba(255,255,255,${LIGHT_HALO_SETTINGS.INNER_INTENSITY})`);
  grad.addColorStop(LIGHT_HALO_SETTINGS.MIDDLE_LIGHT_RADIUS, `rgba(255,255,255,${LIGHT_HALO_SETTINGS.MIDDLE_INTENSITY})`);
  grad.addColorStop(LIGHT_HALO_SETTINGS.OUTER_LIGHT_RADIUS, `rgba(255,255,255,${LIGHT_HALO_SETTINGS.OUTER_INTENSITY})`);
  ctx.fillStyle = grad;
  ctx.beginPath();
  ctx.arc(cx, cy, radius, 0, 2 * Math.PI);
  ctx.fill();
  ctx.restore();

  lightSpriteCache.set(key, sprite);
  return sprite;
}
/**
 * Новая система освещения для интерьеров локаций
 * Вынесена отдельно для чистоты архитектуры
 */

import * as React from 'react';
import { Location } from '../../../shared/types/Location';
import { WorldMap } from '../../../shared/types/World';
import { LocationDecorations, LocationType } from '../../../shared/enums';
import { GameTime, getLightColor, getLightLevel } from '../../utils/time/gameTime';
import { isoToScreen } from '../../utils/coordinates/isometric';
import { CameraRef } from '../../systems/interaction/eventHandlers';
import { 
  LIGHTING_SYSTEM_CONFIG
} from './lightingConstants';

// ===== ОСНОВНАЯ СИСТЕМА ОСВЕЩЕНИЯ =====

// Используем константы из отдельного файла для лучшей организации
const LIGHTING_SETTINGS = LIGHTING_SYSTEM_CONFIG.radial;
const LIGHT_RADIUS_SETTINGS = LIGHTING_SYSTEM_CONFIG.radius;
const LIGHT_HALO_SETTINGS = LIGHTING_SYSTEM_CONFIG.halo;
const DECORATION_LIGHTING = LIGHTING_SYSTEM_CONFIG.decoration;
const INTERIOR_COLOR_FILTER = LIGHTING_SYSTEM_CONFIG.filter;

// ===== ОСНОВНАЯ СИСТЕМА ОСВЕЩЕНИЯ =====

/**
 * Определяет, является ли декорация источником света
 */
function isLightSource(decoration: LocationDecorations): boolean {
  return decoration === LocationDecorations.STREETLIGHT ||
         decoration === LocationDecorations.EXTERIORLIGHT ||
         decoration === LocationDecorations.INTERIORLIGHT;
}

/**
 * Получает радиус освещения для типа источника света
 */
function getLightRadius(decoration: LocationDecorations): number {
  switch (decoration) {
    case LocationDecorations.STREETLIGHT:
      return LIGHT_RADIUS_SETTINGS.STREETLIGHT_RADIUS;
    case LocationDecorations.EXTERIORLIGHT:
      return LIGHT_RADIUS_SETTINGS.EXTERIOR_LIGHT_RADIUS;
    case LocationDecorations.INTERIORLIGHT:
      return LIGHT_RADIUS_SETTINGS.INTERIOR_LIGHT_RADIUS;
    default:
      return LIGHT_RADIUS_SETTINGS.INTERIOR_LIGHT_RADIUS;
  }
}

/**
 * Создает градиент освещения от игрока
 */
function createPlayerLightGradient(
  ctx: CanvasRenderingContext2D,
  playerWorldX: number,
  playerWorldY: number,
  lightRadiusPixels: number
): CanvasGradient {
  const gradient = ctx.createRadialGradient(
    playerWorldX, playerWorldY, 0,
    playerWorldX, playerWorldY, lightRadiusPixels
  );
  
  gradient.addColorStop(0, `rgba(255, 255, 255, 1)`);
  gradient.addColorStop(LIGHTING_SETTINGS.GRADIENT_SMOOTHNESS, `rgba(255, 255, 255, 1)`);
  gradient.addColorStop(1, `rgba(255, 255, 255, 0)`);
  
  return gradient;
}

/**
 * Создает градиент освещения от источника света
 */
function createLightSourceGradient(
  ctx: CanvasRenderingContext2D,
  lightWorldX: number,
  lightWorldY: number,
  lightRadius: number
): CanvasGradient {
  const gradient = ctx.createRadialGradient(
    lightWorldX, lightWorldY, 0,
    lightWorldX, lightWorldY, lightRadius
  );
  
  // Создаем плавный градиент с настраиваемыми зонами
  gradient.addColorStop(0, `rgba(255, 255, 255, ${LIGHT_HALO_SETTINGS.INNER_INTENSITY})`);
  gradient.addColorStop(LIGHT_HALO_SETTINGS.INNER_LIGHT_RADIUS, `rgba(255, 255, 255, ${LIGHT_HALO_SETTINGS.INNER_INTENSITY})`);
  gradient.addColorStop(LIGHT_HALO_SETTINGS.MIDDLE_LIGHT_RADIUS, `rgba(255, 255, 255, ${LIGHT_HALO_SETTINGS.MIDDLE_INTENSITY})`);
  gradient.addColorStop(LIGHT_HALO_SETTINGS.OUTER_LIGHT_RADIUS, `rgba(255, 255, 255, ${LIGHT_HALO_SETTINGS.OUTER_INTENSITY})`);
  
  return gradient;
}/**
 * Обрабатывает источники света в локации
 */
function processLightSources(
  lightMaskCtx: CanvasRenderingContext2D,
  currentLocation: Location,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  tileWidth: number,
  tileHeight: number,
  zoom: number
): void {
  if (!currentLocation.locationMap) return;
  
  const locationSize = currentLocation.locationSize || { x: 10, y: 10 };
  let lightSourcesProcessed = 0;
  
  for (let isoY = 0; isoY < locationSize.y && lightSourcesProcessed < LIGHTING_SETTINGS.MAX_LIGHT_SOURCES; isoY++) {
    for (let isoX = 0; isoX < locationSize.x && lightSourcesProcessed < LIGHTING_SETTINGS.MAX_LIGHT_SOURCES; isoX++) {
      const tileKey = `${isoX},${isoY}`;
      const tile = currentLocation.locationMap[tileKey];
      
      if (tile?.decoration && isLightSource(tile.decoration)) {
        // Получаем экранную позицию источника света с учетом зума
        const { x: lightScreenX, y: lightScreenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);
        const lightWorldX = lightScreenX * zoom + canvasWidth / 2 - cameraX * zoom;
        const lightWorldY = lightScreenY * zoom + canvasHeight / 2 - cameraY * zoom;
        
        // Определяем радиус освещения для данного типа источника с учетом зума
        const lightRadius = getLightRadius(tile.decoration) * zoom;
        
        // Получаем pre-rendered light sprite
        const sprite = getLightSprite(lightRadius, LIGHT_HALO_SETTINGS.LIGHT_ELLIPSE_X, LIGHT_HALO_SETTINGS.LIGHT_ELLIPSE_Y);
        const sw = sprite.width;
        const sh = sprite.height;
        const dx = Math.round(lightWorldX - sw / 2);
        const dy = Math.round(lightWorldY - sh / 2);
        // Копируем только видимую часть спрайта на маску
  lightMaskCtx.drawImage(sprite, dx, dy);
        
        lightSourcesProcessed++;
      }
    }
  }
}

/**
 * Применяет цветовой фильтр в зависимости от локации и времени
 */
export function applyColorFilter(
  ctx: CanvasRenderingContext2D,
  canvasWidth: number,
  canvasHeight: number,
  gameTime: GameTime | undefined,
  currentLocation: Location | null
): void {
  if (!gameTime) return;
  
  const isInterior = currentLocation && 
    (currentLocation.type === LocationType.INDOOR || currentLocation.type === LocationType.UNDERGROUND);
  
  let lightColor;
  
  if (isInterior) {
    // Для интерьеров используем постоянный цветофильтр
    lightColor = INTERIOR_COLOR_FILTER;
  } else {
    // Для улицы используем цвет по времени дня
    lightColor = getLightColor(gameTime);
  }
  
  // Применяем цветовой фильтр только если есть альфа (прозрачность)
  if (lightColor.a > 0) {
    ctx.save();
    
    if (isInterior) {
      // Для интерьеров используем soft-light для теплого эффекта
      ctx.globalCompositeOperation = 'soft-light';
    } else {
      // Для улицы - разные режимы в зависимости от времени дня
      const lightLevel = getLightLevel(gameTime);
      
      if (lightLevel > 0.6) {
        // Дневные цвета - используем soft-light для тонкого эффекта
        ctx.globalCompositeOperation = 'soft-light';
      } else {
        // Ночные цвета - используем multiply для более сильного эффекта
        ctx.globalCompositeOperation = 'multiply';
      }
    }
    
    ctx.fillStyle = `rgba(${lightColor.r}, ${lightColor.g}, ${lightColor.b}, ${lightColor.a})`;
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);
    ctx.restore();
  }
}

/**
 * Основная функция радиального освещения
 */
export function applyRadialLighting(
  ctx: CanvasRenderingContext2D,
  canvasWidth: number,
  canvasHeight: number,
  gameTime: GameTime | undefined,
  currentLocation: Location | null,
  currentWorld: WorldMap | null,
  playerPos: { x: number; y: number } | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number,
  zoom: number
): void {
  if (!gameTime || !currentLocation || !DECORATION_LIGHTING.LIGHTS_ON || !LIGHTING_SETTINGS.ENABLE_RADIAL_LIGHTING) {
    return;
  }
  
  if (!playerPos || !currentWorld?.player?.parameters?.Perception || !cameraRef.current) {
    return;
  }
  
  const isInterior = currentLocation.type === LocationType.INDOOR || currentLocation.type === LocationType.UNDERGROUND;
  // Определяем уникальный ключ локации (id предпочтительно, если нет — name/coords)
  const locationKey = (currentLocation as any)?.id || currentLocation.name || `${currentLocation.type}_${currentLocation.locationSize?.x}x${currentLocation.locationSize?.y}`;
  if (lastLocationKey && lastLocationKey !== locationKey) {
    // Смена локации — сброс масок/кэшей
    resetLightingCache();
  }
  lastLocationKey = locationKey;
  const lightLevel = getLightLevel(gameTime);
  const shouldApplyLighting = lightLevel < 0.6 || isInterior; // Применяем ночью или в интерьерах
  
  if (!shouldApplyLighting) return;
  
  ctx.save();
  
  // Получаем экранную позицию игрока с учетом уже примененного зума в основном рендере
  const { x: playerScreenX, y: playerScreenY } = isoToScreen(playerPos.x, playerPos.y, tileWidth, tileHeight);
  
  // Вычисляем позицию игрока в мировых координатах с учетом камеры и зума
  const playerWorldX = playerScreenX * zoom + canvasWidth / 2 - cameraRef.current.x * zoom;
  const playerWorldY = playerScreenY * zoom + canvasHeight / 2 - cameraRef.current.y * zoom;
  
  // Вычисляем радиус освещения в пикселях с учетом зума
  const perception = currentWorld.player.parameters.Perception || 1;
  const visionRadius = Math.max(1, perception * 1.5); // Базовый радиус в тайлах
  const lightRadiusPixels = visionRadius * LIGHTING_SETTINGS.LIGHT_RADIUS_MULTIPLIER * zoom;
  
  // СОЗДАЕМ OFF-SCREEN CANVAS ДЛЯ ВСЕХ ИСТОЧНИКОВ СВЕТА
  const lightMaskCtxLocal = ensureLightMaskCanvas(canvasWidth, canvasHeight);
  if (lightMaskCtxLocal) {
    // Сначала заливаем весь off-screen canvas черным (затемнение)
    lightMaskCtxLocal.globalCompositeOperation = 'source-over';
    lightMaskCtxLocal.clearRect(0,0,canvasWidth,canvasHeight);
    lightMaskCtxLocal.fillStyle = `rgba(0, 0, 0, ${LIGHTING_SETTINGS.BASE_DARKNESS})`;
    lightMaskCtxLocal.fillRect(0, 0, canvasWidth, canvasHeight);
    lightMaskCtxLocal.globalCompositeOperation = 'destination-out';

    // === PRE-RENDERED SPRITE ДЛЯ ИГРОКА ===
  const playerSprite = getLightSprite(lightRadiusPixels, LIGHT_HALO_SETTINGS.LIGHT_ELLIPSE_X, LIGHT_HALO_SETTINGS.LIGHT_ELLIPSE_Y);
  const sw = playerSprite.width;
  const sh = playerSprite.height;
  const dx = (playerWorldX - sw / 2) | 0;
  const dy = (playerWorldY - sh / 2) | 0;
  lightMaskCtxLocal.drawImage(playerSprite, dx, dy);

    // ДОБАВЛЯЕМ СВЕТ ОТ ВСЕХ ИСТОЧНИКОВ НА ОДИН СЛОЙ
    processLightSources(
      lightMaskCtxLocal, 
      currentLocation, 
      canvasWidth, 
      canvasHeight, 
      cameraRef.current.x, 
      cameraRef.current.y, 
      tileWidth, 
      tileHeight,
      zoom
    );
  }

  // Применяем готовую маску света БЕЗ дополнительного зума
  // (зум уже применен в основном рендере)
  ctx.globalCompositeOperation = 'source-over';
  if (lightMaskCanvas) ctx.drawImage(lightMaskCanvas, 0, 0);

  // Восстанавливаем нормальный режим смешивания
  ctx.globalCompositeOperation = 'source-over';
  ctx.restore();
}
