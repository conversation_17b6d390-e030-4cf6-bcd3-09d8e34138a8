import { WorldMapCell } from '../../shared/types/World';
import { Position } from '../../shared/models/Player';
import { WorldMapDecorations, TerrainType } from 'src/shared';
import {
  SimpleProgressTracker,
  CancellationToken
} from '../../utils/asyncUtils';
import { PatternLoader } from './utils/patternLoader';
import { PatternApplier } from './utils/patternApplier';
import { PatternCollection } from '../../shared/types/PatternTypes';
import { PATTERN_DECORATION_CONFIG } from '../worldGeneratorConstants';

// Типы шаблонных декораций с настраиваемыми весами
const PATTERN_TYPES = [
  { 
    name: 'Горы', 
    collectionName: 'mountainsWM', 
    decoration: WorldMapDecorations.MOUNTAINS,
    weight: PATTERN_DECORATION_CONFIG.PATTERN_WEIGHTS.MOUNTAINS
  },
  { 
    name: 'Реки', 
    collectionName: 'riversWM', 
    decoration: WorldMapDecorations.RIVER,
    weight: PATTERN_DECORATION_CONFIG.PATTERN_WEIGHTS.RIVERS
  },
  { 
    name: 'Озера', 
    collectionName: 'lakesWM', 
    decoration: WorldMapDecorations.LAKE,
    weight: PATTERN_DECORATION_CONFIG.PATTERN_WEIGHTS.LAKES
  },
  { 
    name: 'Городки', 
    collectionName: 'townsWM', 
    decoration: WorldMapDecorations.CITY,
    weight: PATTERN_DECORATION_CONFIG.PATTERN_WEIGHTS.TOWNS
  }
] as const;

/**
 * Новая шаблонная генерация гор, рек, озер и городков
 * Заменяет старую алгоритмическую генерацию
 * Проходит по каждому маркеру и с заданным шансом выбирает что-то из списка
 */
export async function generatePatternDecorationsAsync(
  grid: Record<string, WorldMapCell>,
  worldSize: number,
  rng: () => number,
  cancellationToken?: CancellationToken
): Promise<void> {
  
  // Инициализируем загрузчик шаблонов
  const patternLoader = PatternLoader.getInstance();
  let collections: Map<string, PatternCollection>;
  
  try {
    collections = await patternLoader.loadAllPatternCollections();
    
    // Логируем загруженные коллекции
    for (const [name, collection] of collections) {
      const patternCount = Object.keys(collection.patterns).length;
    }
  } catch (error) {
    // Fallback - пропускаем шаблонную генерацию
    return;
  }

  // Находим все террариан маркеры
  const markers: Position[] = [];
  for (let x = 0; x < worldSize; x++) {
    for (let y = 0; y < worldSize; y++) {
      const cellKey = `${x},${y}`;
      if (grid[cellKey].terrarianMarker === 1) {
        markers.push({ x, y });
      }
    }
  }


  if (markers.length === 0) {
    return;
  }

  // Конвертируем grid в 2D массив для PatternApplier
  const worldMap = gridToMatrix(grid, worldSize);

  let processedMarkers = 0;
  let appliedPatterns = 0;
  let patternStats = { 'Горы': 0, 'Реки': 0, 'Озера': 0, 'Городки': 0 };

  // Проходим по каждому маркеру
  for (const marker of markers) {
    if (cancellationToken?.isCancelled) {
      throw new Error('Operation was cancelled');
    }

    // Проверяем, не занят ли уже этот маркер
    const cellKey = `${marker.x},${marker.y}`;
    if (grid[cellKey].decoration !== WorldMapDecorations.NONE) {
      processedMarkers++;
      continue;
    }

    // Применяем шаблон с заданным шансом
    if (rng() * 100 < PATTERN_DECORATION_CONFIG.APPLICATION_CHANCE) {
      // Выбираем случайный тип декорации
      const selectedType = selectRandomPatternType(PATTERN_TYPES, rng);
      
      if (selectedType) {
        const applied = await applyPatternToMarker(
          worldMap, 
          grid, 
          marker, 
          selectedType, 
          collections,
          patternLoader,
          worldSize, 
          rng
        );
        
        if (applied) {
          appliedPatterns++;
          patternStats[selectedType.name]++;
        }
      }
    }

    processedMarkers++;

    // Обновляем прогресс с заданным интервалом
    if (processedMarkers % PATTERN_DECORATION_CONFIG.PROGRESS_UPDATE_INTERVAL === 0) {
     
     
      await new Promise(resolve => setImmediate(resolve));
    }
  }
}

/**
 * Выбирает случайный тип шаблона на основе весов
 */
function selectRandomPatternType(
  patternTypes: typeof PATTERN_TYPES, 
  rng: () => number
): typeof PATTERN_TYPES[number] | null {
  
  const totalWeight = patternTypes.reduce((sum, type) => sum + type.weight, 0);
  const roll = rng() * totalWeight;
  
  let currentWeight = 0;
  for (const type of patternTypes) {
    currentWeight += type.weight;
    if (roll <= currentWeight) {
      return type;
    }
  }
  
  return patternTypes[0]; // fallback
}

/**
 * Применяет шаблон к конкретному маркеру
 */
async function applyPatternToMarker(
  worldMap: number[][],
  grid: Record<string, WorldMapCell>,
  marker: Position,
  decorationType: typeof PATTERN_TYPES[number],
  collections: Map<string, PatternCollection>,
  patternLoader: PatternLoader,
  worldSize: number,
  rng: () => number
): Promise<boolean> {
  
  const collection = collections.get(decorationType.collectionName);
  
  if (!collection) {
    return false;
  }

  // Получаем случайный шаблон из коллекции
  const pattern = patternLoader.getRandomPattern(collection, rng);
  
  if (!pattern) {
    return false;
  }

  // Проверяе��, поместится ли шаблон
  if (!PatternApplier.canFitPattern(worldMap, pattern, marker.x, marker.y)) {
    // Шаблон не помещается, пропускаем
    return false;
  }

  // Применяем шаблон
  const result = PatternApplier.applyPattern(
    worldMap, 
    pattern, 
    marker.x, 
    marker.y
  );

  if (result.success) {
    // Обновляем grid на основе изменений в worldMap
    updateGridFromMatrix(grid, worldMap, worldSize);
    
    return true;
  }
  
  return false;
}

/**
 * Конвертирует grid в 2D матрицу для PatternApplier
 */
function gridToMatrix(grid: Record<string, WorldMapCell>, worldSize: number): number[][] {
  const matrix: number[][] = [];
  
  for (let y = 0; y < worldSize; y++) {
    const row: number[] = [];
    for (let x = 0; x < worldSize; x++) {
      const cellKey = `${x},${y}`;
      const cell = grid[cellKey];
      // Конвертируем enum в число для матрицы
      const decorationValue = cell.decoration === WorldMapDecorations.NONE ? 0 : getDecorationNumber(cell.decoration);
      row.push(decorationValue);
    }
    matrix.push(row);
  }
  
  return matrix;
}

/**
 * Обновляет grid на основе изменений в матрице
 * Устанавливает соответствующий terrain для каждой декорации
 */
function updateGridFromMatrix(
  grid: Record<string, WorldMapCell>, 
  worldMap: number[][], 
  worldSize: number
): void {
  for (let y = 0; y < worldSize; y++) {
    for (let x = 0; x < worldSize; x++) {
      const cellKey = `${x},${y}`;
      const decorationValue = worldMap[y][x];
      
      if (decorationValue > 0) {
        const decoration = mapValueToDecoration(decorationValue);
        grid[cellKey].decoration = decoration;
        
        // Устанавливаем соответствующий terrain для каждой декорации
        grid[cellKey].terrain = getTerrainForDecoration(decoration);
      }
    }
  }
}

/**
 * Конвертирует enum декорации в число
 */
function getDecorationNumber(decoration: WorldMapDecorations): number {
  switch (decoration) {
    case WorldMapDecorations.MOUNTAINS: return 1;
    case WorldMapDecorations.FOREST: return 2;
    case WorldMapDecorations.BUSHES: return 3;
    case WorldMapDecorations.LAKE: return 4;
    case WorldMapDecorations.RIVER: return 5;
    case WorldMapDecorations.RUINS: return 6;
    case WorldMapDecorations.RUBBLE: return 7;
    case WorldMapDecorations.SWAMP: return 8;
    case WorldMapDecorations.CITY: return 9;
    case WorldMapDecorations.ROAD: return 10;
    default: return 0;
  }
}

/**
 * Маппинг числовых значений на типы декораций
 */
function mapValueToDecoration(value: number): WorldMapDecorations {
  switch (value) {
    case 1: return WorldMapDecorations.MOUNTAINS;
    case 2: return WorldMapDecorations.FOREST;
    case 3: return WorldMapDecorations.BUSHES;
    case 4: return WorldMapDecorations.LAKE;
    case 5: return WorldMapDecorations.RIVER;
    case 6: return WorldMapDecorations.RUINS;
    case 7: return WorldMapDecorations.RUBBLE;
    case 8: return WorldMapDecorations.SWAMP;
    case 9: return WorldMapDecorations.CITY;
    case 10: return WorldMapDecorations.ROAD;
    default: return WorldMapDecorations.NONE;
  }
}

/**
 * Возвращает соответствующий terrain для декорации
 */
function getTerrainForDecoration(decoration: WorldMapDecorations): TerrainType {
  switch (decoration) {
    case WorldMapDecorations.LAKE:
      return TerrainType.WATER;
    
    default:
      return TerrainType.WASTELAND; // Все остальные декорации на пустошах
  }
}