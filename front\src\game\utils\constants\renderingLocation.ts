/**
 * Константы для ренд// Настройки тайлов
export const TILE_GAP = -0.18 // размер отступа между тайламиинга игры
 */

import { useGameStore } from '../../store/gameStore';

// Константы для изометрической проекции
export const BASE_TILE_WIDTH_LOCATION = 70
export const BASE_TILE_HEIGHT_LOCATION = 46

// Настройки рендеринга
export const TARGET_FPS = 500
export const FRAME_DURATION = 1000 / TARGET_FPS


// Настройки зума
export const MIN_ZOOM = 1
export const MAX_ZOOM = 1.3
export const ZOOM_STEP = 0.05

// Настройки тумана войны - простая прозрачность как в Fallout
export const FOG_TILE_OPACITY = 0.2 // Прозрачность клеток с туманом войны

// Настройки тайлов
export const TILE_GAP = -0.1 // размер отступа между тайлами

// Настройки UI обновления
export const CAMERA_UI_UPDATE_INTERVAL = 10 // мс

// Настройки размеров текстур игрока
export const PLAYER_TEXTURE_SIZE = useGameStore.getState().playerLocationPresent ? 150 : 400; // Размер текстур игрока в зависимости от наличия локации
// Настройки размеров текстур декораций
export const DECORATION_TEXTURE_SETTINGS = {
  // Базовые размеры (можно менять для экспериментов)
  DEFAULT_WIDTH: 64,
  DEFAULT_HEIGHT: 64,
  
  // Настройки для каждой декорации: [SCALE_MULTIPLIER, VERTICAL_OFFSET, HORIZONTAL_OFFSET]
  DECORATION_CONFIGS: {
    // Природа и окружение
    ROCKS: [1.3, 5, 0],
    TREE: [5.5, -117, 0],
    LOG: [1.8, -3, 0],
    BUSH: [1.5, -40, 0],
    GRASS: [0.95, -10, 0],
    WATER: [1.0, 0, 0],
    PUDDLE: [1.1, 2, 0],
    MUDD: [1.0, 0, 0],
    
    // Стены и ограждения
    WALL: [2.5, -39, 0],
    MOUNTAINWALL: [2.2, -35, 0],
    FENCE: [2.5, -39, 0],
    PARTITION: [1.4, -39, 0],
    JAILBARS: [2.5, -39, 0],
    
    // Двери и окна
    DOOR: [2.5, -39, 0],
    JAILDOOR: [2.5, -39, 0],
    WINDOW: [2.5, -39, 0],
    GARAGEDOOR: [3.3, -55, 0],
    
    // Мебель и интерьер
    CHAIR: [0.8, -10, 0],
    TABLE: [0.94, -15, 10],
    SOFA: [1.8, -15, 0],
    BAD: [1.9, -20, 0],
    BUNKBED: [1.8, -40, 0],
    ARMCHAIR: [0.9, -15, 0],
    BENCH: [1.1, -13, 0],
    BARSTOOL: [0.5, -15, 0],
    CARPET: [1.5, -10, 0],
    SHELF : [1.2, -55, 0],
    PAINTING : [1.0, -65, 0],
    DOUBLEBED : [1.6, -30, 0],
    
    // Хранение и контейнеры
    RACK: [2.2, -40, 0],
    STORESHELF: [1.3, -28, 0],
    BOOKSHELF: [1.3, -30, 0],
    LOCKER: [1.4, -35, 0],
    CABINET: [1.2, -29, 0],
    SAFE: [0.8, -20, 0],
    TOOLBOX: [1.0, -10, 0],
    WEAPONRACK: [1.1, -27, 0],
    CONTAINER: [0.5, -10, 0],
    MILITARYCONTAINER: [0.5, -10, 0],
    SEACONTAINER: [5.0, -100, 0],
    
    // Техника и электроника
    FRIDGE: [1.2, -30, 0],
    OVEN: [1.1, -15, 0],
    TV: [0.8, -20, 0],
    TERMINAL: [1.0, -35, 0],
    VENDINGMACHINE: [1.4, -32, 0],
    MASSIVECOMPUTER: [1.7, -25, 0],
    BIOMONITOR: [1.0, -26, 0],
    DEFENSESYSTEM: [1.2, -15, 0],
    
    // Сантехника
    TOILET: [1.1, -28, 0],
    SINK: [1.1, -29, 0],
    SHOWER: [1.6, -42, 0],
    BATH: [2.0, -30, 0],
    
    // Освещение
    INTERIORLIGHT: [0.5, -80, 0],
    EXTERIORLIGHT: [3.0, -80, 0],
    STREETLIGHT: [2.5, -65, 0],
    
    // Промышленное оборудование
    WORKBENCH: [1.1, -20, 0],
    LIQUIDTANK: [5.5, -90, 0],
    WARMACHINE: [5.5, -90, 0],
    GASSTATIONPUMP: [1.4, -25, 0],
    PRODUCTIONMACHINE: [1.7, -30, 0],
    
    // Медицинское оборудование
    SURGICALTABLE: [2.0, -45, 0],
    MEDCINECART: [0.8, -17, 0],
    BADSIDETABLE: [0.8, -10, 0],
    FIRSTAID: [0.9, -70, 0],
    
    // Транспорт
    CAR: [5.0, -55, 0],
    TRAIN: [13.2, -120, 0],
    
    // Декоративные элементы
    POSTER: [1.1, -65, 0],
    SIGN: [1.0, -10, 0],
    BLACKBOARD: [1.6, -75, 0],
    WHITEBOARD: [1.0, -25, 0],
    FIREPLACE: [1.0, -25, 0],
    FONTAIN: [3.0, -70, 0],
    
    // Коммерческое оборудование
    BARCOUNTER: [1.2, -15, 0],
    BARSHELF: [1.4, -55, 0],
    CASHREGISTER: [1.2,-15, 0],
    SUBWAYTURNSTILE: [1.3, -15, 0],
    
    // Лестницы и проходы
    STAIRS: [1.1, -10, 8],
    MANHOLECOVER: [0.7, -3, 0],
    
    // Мусор и отходы
    BARREL: [0.8, -6, 0],
    TRASHBIN: [0.6, -15, 0],
    TRASHCONTAINER: [1, -10, 0],
    TIRE: [1.0, -2, 0],
    BOX: [1.3, -25, 0],
    LITTER: [1.3, 6, 0],
    SCELETON: [1.2, -10, 0],
    RUIN: [1.9, -15, 0],
    
    // Игровые столы
    BILLIARDTABLE: [2.0, -20, 0],
    POCKERTABLE: [1.0, -10, 0],
    //банеры 
    BANNERBAR: [1.6, -60, 0],
    BANNERHOSPITAL: [1.6, -60, 0],
    BANNERSHOP: [1.6, -60, 0],
    BANNERHOTEL: [1.6, -60, 0],
    BANNERGUNS_KNIVES: [1.6, -60, 0],
    // ферма 
    HOP: [1.2, -16, 0],
    WHEAT: [1.2, -16, 0],
    CABBAGE: [1.2, -16, 0],
    ONION: [1.2, -16, 0],
    PEPPER: [1.2, -16, 0],
    TOMATO: [1.2, -16, 0],
    BEENS: [1.2, -16, 0],
    CORN: [1.2, -16, 0],
    POTATO: [1.2, -16, 0],
    WITHEREDCROP: [1.2, -16, 0],
    HAYBALE: [1.2, -16, 0],

    SCARECROW: [1.5, -16, 0],

    
    // Разное
    FURNITURE: [1.0, -10, 0],
    TENT: [1.0, -20, 0],
    HAZARD: [1.0, -8, 0],
    PALLET: [1.3, -20, 0],
    UNIVERSALRND: [1.1, -20, 0],
    ROAD: [1.25, -2, 0],
    WATERPUMP: [0.8, -15, 0],
    SHOPPINGCART : [0.8, -15, 0],

  },
  
  // Настройки отрисовки
  ENABLE_SCALING: true,     // Включить/выключить масштабирование
  PRESERVE_ASPECT: true,    // Сохранять пропорции
  CENTER_ON_TILE: true,     // Центрировать на тайле
  ENABLE_OFFSET: true,      // Включить/выключить смещение
  
  // Вспомогательные функции для получения настроек
  getScale: (decoration: string): number => {
    const config = DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS[decoration as keyof typeof DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS];
    return config ? config[0] : 1.0;
  },
  
  getVerticalOffset: (decoration: string): number => {
    const config = DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS[decoration as keyof typeof DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS];
    return config ? config[1] : 0;
  },
  
  getHorizontalOffset: (decoration: string): number => {
    const config = DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS[decoration as keyof typeof DECORATION_TEXTURE_SETTINGS.DECORATION_CONFIGS];
    return config ? config[2] : 0;
  },
}

// Настройки размеров и поворота текстур местности для ЛОКАЦИЙ (TerrainType)
export const LOCATION_TERRAIN_TEXTURE_SETTINGS = {
  // Базовые размеры для текстур местности локаций
  DEFAULT_WIDTH: 80,
  DEFAULT_HEIGHT: 80,
  
  // Масштабирование ширины для разных типов местности в локациях
  WIDTH_SCALE: {
    ASPHALT: 1.0,      // Асфальт стандартно
    BETON: 1.15,        // Бетон чуть шире для лучших стыков
    WOOD: 1.03,         // Дерево стандартно
    METAL: 1.0,        // Металл стандартно  
    GROUND: 1.05,       // Земля стандартно
    WATER: 1.0,   
    TILES: 0.87,     
    WASTELAND: 0.87,    // Пустошь шире
  },
  
  // Масштабирование высоты для разных типов местности в локациях
  HEIGHT_SCALE: {
    ASPHALT: 1.0,      // Асфальт стандартно
    BETON:  0.88,        // Бетон чуть выше для лучших стыков
    WOOD: 0.7,         // Дерево стандартно
    METAL: 1.0,        // Металл стандартно
    GROUND: 0.7,       // Земля стандартно
    WATER: 1.5, 
    TILES: 0.6,     
    WASTELAND: 0.6,    // Пустошь ниже (изометрическая)
  },
  
  // Углы поворота для разных типов местности (в радианах)
  ROTATION_ANGLE: {
    ASPHALT: 0,        // Асфальт без поворота
    BETON: 0.005,      // Бетон поворот на 45 градусов (0.785 радиан)
    WOOD: 0,           // Дерево без поворота
    METAL: 0,          // Металл без поворота
    GROUND: 0,         // Земля без поворота
    WATER: 0,       
    TILES: 0,   // Вода без поворота
    WASTELAND: 0,      // Пустошь без поворота
  },
  
  // Вертикальное смещение текстур местности в локациях
  VERTICAL_OFFSET: {
    ASPHALT: 0,        // Асфальт по центру
    BETON: -1,         // Бетон чуть выше для лучших стыков
    WOOD: 0,           // Дерево по центру
    METAL: 0,          // Металл по центру
    GROUND: 0,         // Земля по центру
    WATER: 0,   
    TILES: 0,       // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Горизонтальное смещение текстур местности в локациях
  HORIZONTAL_OFFSET: {
    ASPHALT: 0,        // Асфальт по центру
    BETON: -1,         // Бетон чуть левее для лучших стыков
    WOOD: 0,           // Дерево по центру
    METAL: 0,          // Металл по центру
    GROUND: 0,         // Земля по центру
    WATER: 0,          // Вода по центру
    WASTELAND: 0,      // Пустошь по центру
  },
  
  // Настройки отрисовки для локаций
  ENABLE_SCALING: true,      // Включить/выключить масштабирование
  ENABLE_ROTATION: true,     // Включить/выключить поворот
  ENABLE_OFFSET: true,       // Включить/выключить смещение
  CLIP_TO_DIAMOND: true,     // Обрезать по ромбу тайла
  SEPARATE_PROPORTIONS: true, // Использовать отдельные пропорции для ширины и высоты
  
  // Индивидуальные настройки для каждого типа текстуры (для отдельного натягивания)
  INDIVIDUAL_TEXTURE_SETTINGS: {
    BETON: {
      // Специальные настройки для бетона из-за проблем со стыками
      OVERLAP_CORRECTION: 2,    // Перекрытие в пикселях для устранения стыков
      EDGE_SMOOTHING: true,     // Сглаживание краев
      PATTERN_MATCH: true,      // Подгонка паттерна на стыках
    },
    WASTELAND: {
      // Настройки для пустоши (если они норм, оставляем как есть)
      TEXTURE_VARIATION: true,  // Использовать вариации текстуры
      BLEND_EDGES: false,       // Не размывать края (работает хорошо)
    },
    ASPHALT: {
      SEAMLESS_TILING: true,    // Бесшовное размножение
    },
    WOOD: {
      GRAIN_ALIGNMENT: true,    // Выравнивание текстуры дерева
    },
    METAL: {
      RUST_VARIATION: true,     // Вариации ржавчины
    },
    GROUND: {
      DIRT_BLENDING: true,      // Смешивание грунта
    },
    WATER: {
      FLOW_ANIMATION: false,    // Пока без анимации течения
    }
  }
}