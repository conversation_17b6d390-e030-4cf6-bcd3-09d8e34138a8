# Build stage
FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Allow passing HOST_LAN_IP at build time so VITE_ vars are baked into the build
ARG HOST_LAN_IP=localhost

# Create a runtime .env file for Vite using the build arg (used during `npm run build`)
RUN printf "VITE_API_BASE_URL=http://%s:3001/api\n" "$HOST_LAN_IP" > .env \
 && printf "VITE_AUTH_SERVICE_URL=http://%s:3001/api/auth\n" "$HOST_LAN_IP" >> .env \
 && printf "VITE_GAME_SERVICE_URL=http://%s:3002/api/game\n" "$HOST_LAN_IP" >> .env \
 && printf "VITE_WORLD_GENERATOR_SERVICE_URL=http://%s:3003/api\n" "$HOST_LAN_IP" >> .env \
 && printf "VITE_SAVE_SERVICE_URL=http://%s:3004/api\n" "$HOST_LAN_IP" >> .env \
 && printf "VITE_AI_SERVICE_URL=http://%s:3005\n" "$HOST_LAN_IP" >> .env

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
