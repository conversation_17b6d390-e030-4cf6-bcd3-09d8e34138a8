import { Injectable, Logger } from '@nestjs/common';
import { CreateWorldDto } from '../dto/create-world.dto';
import { GenerationProgress, CancellationToken } from '../utils/asyncUtils';
import { WorldMap } from '../shared/types/World';
import { v4 as uuidv4 } from 'uuid';

export interface QueueItem {
  id: string;
  sessionId: string;
  userId: string;
  worldData: CreateWorldDto;
  progressCallback?: (progress: GenerationProgress) => void; // Теперь опциональный
  cancellationToken: CancellationToken;
  addedAt: Date;
  startedAt?: Date;
  status: 'waiting' | 'processing' | 'completed' | 'failed' | 'cancelled';
  position?: number;
  world?: WorldMap; // результат генерации (при успехе)
  onComplete?: (item: QueueItem) => Promise<void> | void; // колбэк финализации (сохранение и уведомление)
}

@Injectable()
export class GenerationQueue {
  private readonly logger = new Logger(GenerationQueue.name);
  private queue: QueueItem[] = [];
  private processing: QueueItem[] = [];
  private maxConcurrent: number;
  private progressService?: any; // Добавляем ProgressService

  constructor(maxConcurrent: number = 3, progressService?: any) {
    this.maxConcurrent = maxConcurrent;
    this.progressService = progressService;
    this.logger.log(`Очередь генерации инициализирована. Максимум одновременных генераций: ${maxConcurrent}`);
  }

  /**
   * Добавляет задачу в очередь
   */
  addToQueue(
    sessionId: string,
    userId: string,
    worldData: CreateWorldDto,
    progressCallback: ((progress: GenerationProgress) => void) | null,
    cancellationToken: CancellationToken,
    onComplete?: (item: QueueItem) => Promise<void> | void
  ): QueueItem {
    const queueItem: QueueItem = {
      id: uuidv4(),
      sessionId,
      userId,
      worldData,
      progressCallback: progressCallback || undefined,
      cancellationToken,
      addedAt: new Date(),
      status: 'waiting',
      onComplete
    };

    this.queue.push(queueItem);
    this.updateQueuePositions();
    
    this.logger.log(`Задача ${sessionId} добавлена в очередь. Позиция: ${queueItem.position}`);
    
    // Уведомляем пользователя о позиции в очереди
    this.notifyQueuePosition(queueItem);
    
    // Пытаемся запустить обработку
    this.processNext();
    
    return queueItem;
  }

  /**
   * Удаляет задачу из очереди (отмена)
   */
  removeFromQueue(sessionId: string): boolean {
    // Ищем в очереди ожидания
    const queueIndex = this.queue.findIndex(item => item.sessionId === sessionId);
    if (queueIndex !== -1) {
      const item = this.queue[queueIndex];
      item.status = 'cancelled';
      item.cancellationToken.cancel();
      this.queue.splice(queueIndex, 1);
      this.updateQueuePositions();
      this.logger.log(`Задача ${sessionId} удалена из очереди ожидания`);
      return true;
    }

    // Ищем в обрабатываемых
    const processingItem = this.processing.find(item => item.sessionId === sessionId);
    if (processingItem) {
      processingItem.status = 'cancelled';
      processingItem.cancellationToken.cancel();
      this.logger.log(`Задача ${sessionId} отменена во время обработки`);
      return true;
    }

    return false;
  }

  /**
   * Обрабатывает следующую задачу в очереди
   */
  private async processNext(): Promise<void> {
    // Проверяем, можем ли запустить новую задачу
    if (this.processing.length >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    const item = this.queue.shift();
    if (!item) return;

    // Проверяем, не была ли задача отменена
    if (item.cancellationToken.isCancelled) {
      item.status = 'cancelled';
      this.logger.log(`Задача ${item.sessionId} была отменена до начала обработки`);
      this.processNext(); // Пытаемся обработать следующую
      return;
    }

    // Перемещаем в обрабатываемые
    item.status = 'processing';
    item.startedAt = new Date();
    this.processing.push(item);
    this.updateQueuePositions();

    this.logger.log(`Начинаем обработку задачи ${item.sessionId}. Активных генераций: ${this.processing.length}`);

    // Уведомляем о начале обработки
    if (item.progressCallback) {
      item.progressCallback({
        stage: 'Начало генерации',
        progress: 0,
        currentOperation: 'Генерация началась',
        estimatedTimeRemaining: 0
      });
    }

    // Альтернативно отправляем через ProgressService
    if (this.progressService) {
      this.progressService.sendProgress(item.sessionId, 0, 'Начало генерации', 'Генерация началась');
    }

    try {
      // Импортируем и запускаем генерацию
      const { generateBaseWorldAsync } = await import('../generators/worldGenerator');

      const world = await generateBaseWorldAsync(
        item.worldData,
        item.progressCallback,
        item.cancellationToken,
        this.progressService, // Передаем ProgressService
        item.sessionId // Передаем sessionId
      );

      if (!item.cancellationToken.isCancelled) {
        item.status = 'completed';
        item.world = world;
        this.logger.log(`Задача ${item.sessionId} успешно завершена`);
      }

    } catch (error) {
      if (error.message === 'Operation was cancelled') {
        item.status = 'cancelled';
        this.logger.log(`Задача ${item.sessionId} была отменена`);
      } else {
        item.status = 'failed';
        this.logger.error(`Задача ${item.sessionId} завершилась с ошибкой:`, error.message);
      }
    } finally {
      // Удаляем из обрабатываемых
      const index = this.processing.findIndex(p => p.sessionId === item.sessionId);
      if (index !== -1) {
        this.processing.splice(index, 1);
      }

      this.logger.log(`Задача ${item.sessionId} завершена. Активных генераций: ${this.processing.length}`);

      // Вызываем колбэк финализации один раз (без повторной генерации)
      if (item.onComplete) {
        try {
          await item.onComplete(item);
        } catch (cbErr: any) {
          this.logger.error(`onComplete callback error for ${item.sessionId}: ${cbErr.message}`);
        }
      }
      
      // Обновляем позиции и пытаемся запустить следующую
      this.updateQueuePositions();
      this.processNext();
    }
  }

  /**
   * Обновляет позиции в очереди
   */
  private updateQueuePositions(): void {
    this.queue.forEach((item, index) => {
      item.position = index + 1;
    });

    // Уведомляем всех в очереди об обновлении позиций
    this.queue.forEach(item => {
      this.notifyQueuePosition(item);
    });
  }

  /**
   * Уведомляет пользователя о позиции в очереди
   */
  private notifyQueuePosition(item: QueueItem): void {
    if (item.status === 'waiting' && item.position && item.progressCallback) {
      const estimatedWaitTime = this.calculateEstimatedWaitTime(item.position);

      item.progressCallback({
        stage: 'В очереди',
        progress: 0,
        currentOperation: `Позиция в очереди: ${item.position}. Активных генераций: ${this.processing.length}/${this.maxConcurrent}`,
        estimatedTimeRemaining: estimatedWaitTime
      });
    }

    // Альтернативно отправляем через ProgressService если доступен
    if (item.status === 'waiting' && item.position && this.progressService) {
      this.progressService.sendProgress(
        item.sessionId,
        0,
        'В очереди',
        `Позиция в очереди: ${item.position}. Активных генераций: ${this.processing.length}/${this.maxConcurrent}`
      );
    }
  }

  /**
   * Рассчитывает примерное время ожидания
   */
  private calculateEstimatedWaitTime(position: number): number {
    const averageGenerationTime = 60000; // 1 минута на генерацию (примерно)
    const waitingAhead = Math.max(0, position - 1);
    const slotsAvailable = Math.max(1, this.maxConcurrent - this.processing.length);
    
    return Math.ceil(waitingAhead / slotsAvailable) * averageGenerationTime;
  }

  /**
   * Получает статистику очереди
   */
  getQueueStats() {
    return {
      waiting: this.queue.length,
      processing: this.processing.length,
      maxConcurrent: this.maxConcurrent,
      queue: this.queue.map(item => ({
        sessionId: item.sessionId,
        userId: item.userId,
        position: item.position,
        addedAt: item.addedAt,
        status: item.status
      })),
      activeProcessing: this.processing.map(item => ({
        sessionId: item.sessionId,
        userId: item.userId,
        startedAt: item.startedAt,
        status: item.status
      }))
    };
  }

  /**
   * Получает информацию о конкретной задаче
   */
  getTaskInfo(sessionId: string) {
    const queueItem = this.queue.find(item => item.sessionId === sessionId);
    if (queueItem) {
      return {
        status: queueItem.status,
        position: queueItem.position,
        addedAt: queueItem.addedAt,
        estimatedWaitTime: queueItem.position ? this.calculateEstimatedWaitTime(queueItem.position) : 0
      };
    }

    const processingItem = this.processing.find(item => item.sessionId === sessionId);
    if (processingItem) {
      return {
        status: processingItem.status,
        startedAt: processingItem.startedAt,
        addedAt: processingItem.addedAt
      };
    }

    return null;
  }

  /**
   * Изменяет максимальное количество одновременных генераций
   */
  setMaxConcurrent(max: number): void {
    this.maxConcurrent = Math.max(1, max);
    this.logger.log(`Максимум одновременных генераций изменен на: ${this.maxConcurrent}`);
    
    // Пытаемся запустить дополнительные задачи, если увеличили лимит
    while (this.processing.length < this.maxConcurrent && this.queue.length > 0) {
      this.processNext();
    }
  }
}