import { useState, useEffect } from 'react'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT, FRAME_DURATION, getImcompSize } from '../utils/constants/rendering'
import { terrainTextureManager, decorationTextureManager } from './isometric/renderUtils'
import { createContextMenuHandler } from '../systems/interaction/eventHandlers'
import { gameTimeManager } from '../utils/time/gameTimeManager'
import { GameTime } from '../utils/time/gameTime'

/**
 * Хук для адаптивного размера экрана с максимальными ограничениями.
 * Возвращает { width, height } для canvas.
 */
export function useAdaptiveScreenSize(maxWidth?: number, maxHeight?: number) {
  // get dynamic defaults from rendering module (updated by applyImcompSize)фыв
  const defaults = getImcompSize()
  const usedMaxWidth = maxWidth ?? defaults.width ?? IMCOMP_MAX_WIDTH
  const usedMaxHeight = maxHeight ?? defaults.height ?? IMCOMP_MAX_HEIGHT

  // If a max value is <= 1, treat it as a ratio of window size (e.g. 0.83 = 83%)
  const resolveMax = (value: number) => {
    if (value <= 1) {
      // guard: value might be 0 (user set 0) — allow it but avoid negative
      return Math.max(0, value) // keep ratio for later multiplication
    }
    return value
  }
  const resolvedMaxWidth = resolveMax(usedMaxWidth)
  const resolvedMaxHeight = resolveMax(usedMaxHeight)

  const isClient = typeof window !== 'undefined'

  const [screenSize, setScreenSize] = useState(() => ({
  width: isClient ? Math.min(window.innerWidth, typeof resolvedMaxWidth === 'number' && resolvedMaxWidth <= 1 ? Math.round(window.innerWidth * resolvedMaxWidth) : resolvedMaxWidth) : (typeof resolvedMaxWidth === 'number' && resolvedMaxWidth <= 1 ? Math.round((defaults.width ?? IMCOMP_MAX_WIDTH) * resolvedMaxWidth) : resolvedMaxWidth),
  height: isClient ? Math.min(window.innerHeight, typeof resolvedMaxHeight === 'number' && resolvedMaxHeight <= 1 ? Math.round(window.innerHeight * resolvedMaxHeight) : resolvedMaxHeight) : (typeof resolvedMaxHeight === 'number' && resolvedMaxHeight <= 1 ? Math.round((defaults.height ?? IMCOMP_MAX_HEIGHT) * resolvedMaxHeight) : resolvedMaxHeight)
  }))

  useEffect(() => {
    if (!isClient) return
    const handleResize = () => {
      const computedWidth = typeof resolvedMaxWidth === 'number' && resolvedMaxWidth <= 1 ? Math.round(window.innerWidth * resolvedMaxWidth) : resolvedMaxWidth
      const computedHeight = typeof resolvedMaxHeight === 'number' && resolvedMaxHeight <= 1 ? Math.round(window.innerHeight * resolvedMaxHeight) : resolvedMaxHeight
      setScreenSize({
        width: Math.min(window.innerWidth, computedWidth),
        height: Math.min(window.innerHeight, computedHeight)
      })
    }
    window.addEventListener('resize', handleResize)
    // run once to ensure correct initial values (handles F11/fullscreen changes)
    handleResize()
    return () => window.removeEventListener('resize', handleResize)
  }, [usedMaxWidth, usedMaxHeight])

  return screenSize
}

/**
 * Хук для хранения выбранной клетки карты
 * Возвращает выбранную клетку и функцию для её обновления
 */
export function useCellTarget() {
  const [cellTarget, setCellTarget] = useState<{
    isoX: number;
    isoY: number;
    tileData: any;
  } | null>(null)
  return [cellTarget, setCellTarget] as const
}

/**
 * Хук для предзагрузки текстур местности
 */
export const useTerrainTextures = () => {
  const [texturesLoaded, setTexturesLoaded] = useState(false)

  useEffect(() => {
    const preloadTextures = async () => {
      try {
        await terrainTextureManager.preloadAllTextures()
        setTexturesLoaded(true)
      } catch (error) {
      }
    }

    preloadTextures()
  }, [])

  return { texturesLoaded, terrainTextureManager }
}

/**
 * Хук для предзагрузки текстур декораций
 */
export const useDecorationTextures = () => {
  const [texturesLoaded, setTexturesLoaded] = useState(false)

  useEffect(() => {
    const preloadTextures = async () => {
      try {
        await decorationTextureManager.preloadAllTextures()
        setTexturesLoaded(true)
      } catch (error) {
      }
    }

    preloadTextures()
  }, [])

  return { texturesLoaded, decorationTextureManager }
}

/**
 * Хук для циклической отрисовки - основной рендер-цикл
 */
export const useRenderLoop = (draw: () => void) => {
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0
    let frameCount = 0

    const renderLoop = (time: number) => {
      frameCount++

      // Рисуем каждый кадр для плавности (можно изменить на каждый 5й кадр если нужно)
      if (time - lastRenderTime >= FRAME_DURATION) {
        draw()
        lastRenderTime = time
      }

      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [draw]) // Только draw в зависимостях
}

/**
 * Хук для отключения контекстного меню
 */
export const useContextMenuDisable = (canvasRef: React.RefObject<HTMLCanvasElement>) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const handler = createContextMenuHandler()
    canvas.addEventListener('contextmenu', handler)
    return () => canvas.removeEventListener('contextmenu', handler)
  }, [canvasRef])
}

/**
 * Хук для начальной отрисовки
 */
export const useInitialDraw = (draw: () => void) => {
  useEffect(() => {
    draw()
  }, [draw])
}

/**
 * Хук для работы с игровым временем
 */
export const useGameTime = () => {
  const [gameTime, setGameTime] = useState<GameTime>(() => gameTimeManager.getCurrentTime())

  useEffect(() => {
    const unsubscribe = gameTimeManager.addTimeUpdateCallback(setGameTime)
    return unsubscribe
  }, [])

  return gameTime
}

/**
 * Хук для управления игровым временем
 */
export const useGameTimeControl = () => {
  const gameTime = useGameTime()

  const getCurrentTime = () => gameTimeManager.getCurrentTime()
  const fastForward = (minutes: number) => gameTimeManager.fastForward(minutes)
  const setTime = (time: GameTime) => gameTimeManager.setTime(time)

  return {
    gameTime,
    getCurrentTime,
    fastForward,
    setTime
  }
}