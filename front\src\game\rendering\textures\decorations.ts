import { LocationDecorations, WorldMapDecorations } from '../../../shared/enums';


// Пути к текстурам декораций
// Ключи соответствуют значениям enum WorldMapDecorations
export const DECORATION_TEXTURES: Partial<Record<WorldMapDecorations, string[]>> = {
  [WorldMapDecorations.FOREST]: [
    '/textures/worldMap/decorations/forest/forest_1.png',
    '/textures/worldMap/decorations/forest/forest_2.png',
    '/textures/worldMap/decorations/forest/forest_3.png',
    '/textures/worldMap/decorations/forest/forest_4.png',
  ],
  [WorldMapDecorations.MOUNTAINS]: [
    '/textures/worldMap/decorations/mountains/1.png',
    '/textures/worldMap/decorations/mountains/2.png',
    '/textures/worldMap/decorations/mountains/3.png',
    '/textures/worldMap/decorations/mountains/4.png',
  ],
  [WorldMapDecorations.ROAD]: [
    '/textures/worldMap/decorations/roads/1.png',
    
    '/textures/worldMap/decorations/roads/2.png',
    '/textures/worldMap/decorations/roads/3.png',
    '/textures/worldMap/decorations/roads/4.png',
  ],
  [WorldMapDecorations.SWAMP]: [
      '/textures/worldMap/decorations/swamp/1.png',
      '/textures/worldMap/decorations/swamp/2.png',
      '/textures/worldMap/decorations/swamp/3.png',
      '/textures/worldMap/decorations/swamp/4.png',
  ],
  [WorldMapDecorations.CITY]: [
      '/textures/worldMap/decorations/city/1.png',
      '/textures/worldMap/decorations/city/2.png',
      '/textures/worldMap/decorations/city/3.png',
      '/textures/worldMap/decorations/city/4.png',
  ],
  [WorldMapDecorations.RUINS]: [
      '/textures/worldMap/decorations/ruins/1.png',
      '/textures/worldMap/decorations/ruins/2.png',
      '/textures/worldMap/decorations/ruins/3.png',
      '/textures/worldMap/decorations/ruins/4.png',
  ],
  [WorldMapDecorations.LAKE]: [
      '/textures/worldMap/decorations/lake/1/1.png',
      '/textures/worldMap/decorations/lake/1/2.png',
      '/textures/worldMap/decorations/lake/1/3.png',
      '/textures/worldMap/decorations/lake/1/4.png',
  ],
  [WorldMapDecorations.RUBBLE]: [
      '/textures/worldMap/decorations/rubble/1.png',
      '/textures/worldMap/decorations/rubble/2.png',
      '/textures/worldMap/decorations/rubble/3.png',
      '/textures/worldMap/decorations/rubble/4.png',
  ],
  [WorldMapDecorations.BRIDGE]: [
      '/textures/worldMap/decorations/bridge/bridge_1.png',
  ],
  [WorldMapDecorations.BUSHES]: [
      '/textures/worldMap/decorations/bushes/1.png',
      
      '/textures/worldMap/decorations/bushes/2.png',
      '/textures/worldMap/decorations/bushes/3.png',
      '/textures/worldMap/decorations/bushes/4.png',
  ]
};

// DECORATION_LOCATION_TEXTURES removed — paths are generated conventionally
// All location decorations should use the side/no-sides system and the
// standardized folder layout: /textures/Location/decorations/<decoration>/<n>.png
// or with subfolders for day/night: /textures/Location/decorations/<decoration>/day/<n>.png