import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'
import { IsometricMap } from './rendering/isometric/isomerticWorldMap'
import { SaveWorldModal } from '../components/SaveWorldModal'
import { NotificationModal } from '../components/NotificationModal'
import TextureLoadingScreen from '../components/TextureLoadingScreen'
import { gameTimeManager } from './utils/time/gameTimeManager'
import { saveWorldWithNewName, replaceWorldById } from '../api/worldsApi'
import styles from './GamePage.module.css'
import { ensurePlayerThoughts } from './index'
import { useGameStore } from '@/game/store/gameStore'
import GameSettings from './ui/system/GameSettings'

const GamePage: React.FC = () => {
  const navigate = useNavigate()
  const { currentWorld, currentWorldId, settings, updateSettings, texturesLoaded, setTexturesLoaded } = useGameStore()
  const { logout } = useAuthStore()
  
  // Состояние для UI
  const [showSettings, setShowSettings] = useState(false)
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [showLoadDialog, setShowLoadDialog] = useState(false)
  const [showExitDialog, setShowExitDialog] = useState(false)
  const [showNotification, setShowNotification] = useState(false)
  const [notificationData, setNotificationData] = useState({ title: '', message: '', type: 'success' as 'success' | 'error' })
  const [isMuted, setIsMuted] = useState(!settings.soundEnabled)
  const [isSaving, setIsSaving] = useState(false)
  // Отключаем контекстное меню (правый клик) на всей странице GamePage
  useEffect(() => {
    const handler = (e: MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }
    document.addEventListener('contextmenu', handler)
    return () => document.removeEventListener('contextmenu', handler)
  }, [])

  useEffect(() => {
  // Инициализация системы случайных мыслей игрока (если еще не активна)
  ensurePlayerThoughts()

    // Если нет выбранного мира, перенаправляем в меню
    if (!currentWorld) {
      navigate('/menu')
    } else {
      // Запускаем систему времени при загрузке мира
      gameTimeManager.start()
    }
    
    return () => {
      // Останавливаем систему времени при выходе
      gameTimeManager.stop()
    }
  }, [currentWorld, navigate])

  const showNotificationMessage = (title: string, message: string, type: 'success' | 'error' = 'success') => {
    setNotificationData({ title, message, type })
    setShowNotification(true)
  }

  const handleSave = () => {
    setShowSaveDialog(true)
  }

  const handleSaveWorld = async (name: string, isNewSave: boolean) => {
    if (!currentWorld) {
      showNotificationMessage('Ошибка', 'Нет данных для сохранения', 'error')
      return
    }
    const { syncLocationToWorld } = useGameStore.getState()
    await syncLocationToWorld()
    
    // Получаем обновленный мир после синхронизации
    const updatedWorld = useGameStore.getState().currentWorld
    if (!updatedWorld) {
      showNotificationMessage('Ошибка', 'Нет данных для сохранения после синхронизации', 'error')
      return
    }
    
    setIsSaving(true)
    try {
      let result
      if (isNewSave) {
        result = await saveWorldWithNewName(updatedWorld, name)
      } else {
        if (!currentWorldId) {
          showNotificationMessage('Ошибка', 'Не найден ID текущего мира', 'error')
          return
        }
        result = await replaceWorldById(currentWorldId, updatedWorld)
      }
      
      if (result.success) {
        showNotificationMessage(
          'Успешно!', 
          isNewSave ? `Мир "${name}" успешно сохранен` : 'Мир успешно перезаписан'
        )
      } else {
        showNotificationMessage('Ошибка', result.error || 'Неизвестная ошибка', 'error')
      }
    } catch (error) {
      showNotificationMessage('Ошибка', `Ошибка при сохранении: ${error}`, 'error')
    } finally {
      setIsSaving(false)
    }
  }

  const handleLoad = () => {
    setShowLoadDialog(true)
  }

  const handleExit = () => {
    setShowExitDialog(true)
  }

  const confirmExit = () => {
    navigate('/menu')
  }

  const toggleMute = () => {
    const newMuted = !isMuted
    setIsMuted(newMuted)
    updateSettings({ soundEnabled: !newMuted, musicEnabled: !newMuted })
  }

  const handleTexturesLoaded = () => {
    setTexturesLoaded(true)
  }

  // Проверка: если в этой вкладке уже была полная предзагрузка (по версии) – пропускаем экран
  useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        const { getTextureInfo, isAllTexturesPreloaded } = await import('./rendering/textures/TexturePreloader');
        const textureInfo = getTextureInfo();
        const sessionVersion = sessionStorage.getItem('texturesPreloadedVersion');
        if (!cancelled && sessionVersion === textureInfo.generatedAt && isAllTexturesPreloaded()) {
          setTexturesLoaded(true);
        }
      } catch (_) {
        // игнорируем – просто покажем загрузку
      }
    })();
    return () => { cancelled = true };
  }, [setTexturesLoaded])

  // Показываем загрузочный экран если текстуры не загружены
  if (!texturesLoaded) {
    return <TextureLoadingScreen onComplete={handleTexturesLoaded} />
  }

  if (!currentWorld) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingContent}>
          <div className={styles.loadingSpinner}></div>
          <p>Загрузка мира...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.gamePage}>
      {/* Игровой интерфейс на весь экран */}
      <IsometricMap
        currentWorld={currentWorld}
        onSave={handleSave}
        onLoad={handleLoad}
        onSettings={() => setShowSettings(true)}
        onExit={handleExit}
        onToggleMute={toggleMute}
        isMuted={isMuted}
      />

      {/* Диалог настроек */}
      {showSettings && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <GameSettings onClose={() => setShowSettings(false)} />
          </div>
        </div>
      )}

      {/* Диалог сохранения */}
      <SaveWorldModal
        isOpen={showSaveDialog}
        onClose={() => setShowSaveDialog(false)}
        onSave={handleSaveWorld}
        currentWorldName={currentWorld?.name}
      />

      {/* Уведомления */}
      <NotificationModal
        isOpen={showNotification}
        onClose={() => setShowNotification(false)}
        title={notificationData.title}
        message={notificationData.message}
        type={notificationData.type}
      />

      {/* Диалог загрузки */}
      {showLoadDialog && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.dialogHeader}>
              <h3>Загрузить игру</h3>
            </div>
            <div className={styles.dialogBody}>
              <p>Вы уверены, что хотите загрузить сохранение?</p>
              <p className={styles.warning}>Несохраненный прогресс будет потерян!</p>
            </div>
            <div className={styles.dialogActions}>
              <button className={styles.confirmButton}>
                Да, загрузить
              </button>
              <button 
                className={styles.cancelButton}
                onClick={() => setShowLoadDialog(false)}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог выхода */}
      {showExitDialog && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.dialogHeader}>
              <h3>Выйти в меню</h3>
            </div>
            <div className={styles.dialogBody}>
              <p>Вы уверены, что хотите выйти в главное меню?</p>
              <p className={styles.warning}>Несохраненный прогресс будет потерян!</p>
            </div>
            <div className={styles.dialogActions}>
              <button 
                className={styles.confirmButton}
                onClick={confirmExit}
              >
                Да, выйти
              </button>
              <button 
                className={styles.cancelButton}
                onClick={() => setShowExitDialog(false)}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default GamePage
